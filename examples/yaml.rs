/// A program that serializes a Rust structure to YAML and pretty-prints the result
use bat::<PERSON><PERSON><PERSON><PERSON>;
use serde::Serialize;

#[derive(Serialize)]
struct Person {
    name: String,
    height: f64,
    adult: bool,
    children: Vec<Person>,
}

fn main() {
    let person = Person {
        name: String::from("<PERSON>"),
        height: 1.76f64,
        adult: true,
        children: vec![Person {
            name: String::from("<PERSON>"),
            height: 1.32f64,
            adult: false,
            children: vec![],
        }],
    };

    let bytes = serde_yaml::to_vec(&person).unwrap();
    PrettyPrinter::new()
        .language("yaml")
        .line_numbers(true)
        .grid(true)
        .header(true)
        .input_from_bytes_with_name(&bytes, "person.yaml")
        .print()
        .unwrap();
}
