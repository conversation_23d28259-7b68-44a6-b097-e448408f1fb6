environment:
  global:
    TARGET: x86_64-pc-windows-msvc
    RUST_VERSION: stable
    CRATE_NAME: bat
    CARGO_HOME: "c:\\cargo"
    RUSTUP_HOME: "c:\\rustup"

install:
  - appveyor DownloadFile https://win.rustup.rs/ -FileName rustup-init.exe
  - rustup-init.exe -y --default-host %TARGET% --profile minimal
  - set PATH=%PATH%;C:\cargo\bin
  - rustc -Vv
  - cargo -V

build: false
test_script:
  - cargo test --target %TARGET% --verbose
  - cargo run --target %TARGET% -- src/bin/bat/main.rs README.md --paging=never

before_deploy:
  # Generate artifacts for release
  - cargo build --bins --release --verbose
  - ps: ci\before_deploy.ps1

deploy:
  description: 'Automatically deployed release'
  artifact: /.*\.zip/
  # Here's how:
  # - Go to 'https://github.com/settings/tokens/new' and generate a Token with only the
  # `public_repo` scope enabled
  # - Then go to 'https://ci.appveyor.com/tools/encrypt' and enter the newly generated token.
  # - Enter the "encrypted value" below
  auth_token:
    secure: oO4/J+hcFXgXcEqEc8gzlQNqLG0IxU78s4EyH+uczGdsyWajb3yipxPR6nXUvmoj
  provider: GitHub
  on:
    RUST_VERSION: stable
    appveyor_repo_tag: true

cache:
  - C:\Users\<USER>\.cargo\registry
  - target

notifications:
  - provider: Email
    on_build_success: false
    on_build_failure: false
    on_build_status_changed: false
