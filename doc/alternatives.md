# Alternatives

The following table tries to give an overview *from `bat`s perspective*, i.e. we only compare
categories which are relevant for `bat`. Some of these projects have completely different goals and
if you are not looking for a program like `bat`, this comparison might not be for you.

|                                              | bat                                                                 | [pygments](http://pygments.org/) | [highlight](http://www.andre-simon.de/doku/highlight/highlight.php) | [ccat](https://github.com/jingweno/ccat) | [source-highlight](https://www.gnu.org/software/src-highlite/) | [hicat](https://github.com/rstacruz/hicat)          | [coderay](https://github.com/rubychan/coderay)      | [rouge](https://github.com/jneen/rouge)             |
|----------------------------------------------|---------------------------------------------------------------------|----------------------------------|---------------------------------------------------------------------|------------------------------------------|----------------------------------------------------------------|-----------------------------------------------------|-----------------------------------------------------|-----------------------------------------------------|
| Drop-in `cat` replacement                    | :heavy_check_mark: [*](https://github.com/sharkdp/bat/issues/134)   | :x:                              | :x:                                                                 | (:heavy_check_mark:)                     | :x:                                                            | :x: [*](https://github.com/rstacruz/hicat/issues/6) | :x:                                                 | :x:                                                 |
| Git integration                              | :heavy_check_mark:                                                  | :x:                              | :x:                                                                 | :x:                                      | :x:                                                            | :x:                                                 | :x:                                                 | :x:                                                 |
| Automatic paging                             | :heavy_check_mark:                                                  | :x:                              | :x:                                                                 | :x:                                      | :x:                                                            | :heavy_check_mark:                                  | :x:                                                 | :x:                                                 |
| Languages (circa)                            | 110                                                                 | 300                              | 200                                                                 | 7                                        | 80                                                             | 130                                                 | 30                                                  | 130                                                 |
| Extensible (languages, themes)               | :heavy_check_mark:                                                  | (:heavy_check_mark:)             | (:heavy_check_mark:)                                                | :x:                                      | (:heavy_check_mark:)                                           | :x:                                                 | :x:                                                 | :x:                                                 |
| Advanced highlighting (e.g. nested syntaxes) | :heavy_check_mark:                                                  | :heavy_check_mark:               | (:heavy_check_mark:) ?                                              | :x:                                      | :heavy_check_mark:                                             | :heavy_check_mark:                                  | :heavy_check_mark:                                  | :heavy_check_mark:                                  |
| Execution time [ms] (`jquery-3.3.1.js`)      | 624                                                                 | 789                              | 400                                                                 | 80                                       | 300                                                            | 316                                                 | 157                                                 | 695                                                 |
| Execution time [ms] (`miniz.c`)              | 66                                                                  | 656                              | 26                                                                  | 8                                        | 53                                                             | 141                                                 | 75                                                  | 254                                                 |
| Execution time [ms] (370 kB XML file)        | 238                                                                 | 487                              | 129                                                                 | 111                                      | 110                                                            | 339                                                 | 147                                                 | 359                                                 |

If you think that some entries in this table are outdated or wrong, please open a ticket or pull
request.

Some other alternatives that are also related, but not yet included in the table:
- [lesspipe](https://github.com/wofr06/lesspipe)
- [vimpager](https://github.com/rkitover/vimpager)
