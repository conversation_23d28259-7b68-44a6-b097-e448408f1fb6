---
name: Bug Report
about: Report a bug.
title: ""
labels: bug
assignees: ''

---

<!--
Hey there, thanks for creating an issue!

In order to reproduce your issue, we might need to know a little bit more about the environment
which you're running `bat` on.

If you're on Linux or MacOS:
  Please run the script at https://github.com/sharkdp/bat/blob/master/diagnostics/info.sh and
  paste the output at the bottom of the bug report.

If you're on Windows:
  Please tell us about your Windows Version (e.g. "Windows 10 1908") at the
  bottom of the bug report.
-->

**What version of `bat` are you using?**
[paste the output of `bat --version` here]

**Describe the bug you encountered:**
...

**Describe what you expected to happen?**
...

**How did you install `bat`?**
apt-get, homebrew, GitHub release, etc.

---

[paste the output of `info.sh` here]
