language: rust

matrix:
  include:
    # Stable channel.
    - os: linux
      rust: stable
      env: TARGET=x86_64-unknown-linux-gnu
    - os: linux
      rust: stable
      env:
        - TARGET=x86_64-unknown-linux-musl
        - CC_x86_64_unknown_linux_musl=/usr/bin/musl-gcc
    - os: linux
      rust: stable
      env: TARGET=i686-unknown-linux-gnu
    - os: osx
      rust: stable
      env: TARGET=x86_64-apple-darwin
    - os: linux
      rust: stable
      env:
        - TARGET=arm-unknown-linux-gnueabihf
        - CC_arm_unknown_linux_gnueabihf=/usr/bin/arm-linux-gnueabihf-gcc-4.8
        - CARGO_TARGET_ARM_UNKNOWN_LINUX_GNUEABIHF_LINKER=arm-linux-gnueabihf-gcc-4.8
    - os: linux
      rust: stable
      env:
        - TARGET=aarch64-unknown-linux-gnu
        - CC_aarch64-unknown-linux-gnu=/usr/bin/aarch64-linux-gnu-gcc-4.8
        - CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc-4.8

    # Minimum Rust supported channel.
    - os: linux
      rust: 1.40.0
      env: TARGET=x86_64-unknown-linux-gnu
    - os: linux
      rust: 1.40.0
      env:
        - TARGET=x86_64-unknown-linux-musl
        - CC_x86_64_unknown_linux_musl=/usr/bin/musl-gcc
    - os: osx
      rust: 1.40.0
      env: TARGET=x86_64-apple-darwin

    # Disable nightly for now
    # # Code formatting check
    # - os: linux
    #   rust: nightly
    #   # skip the global install step
    #   install:
    #     - cargo install --debug --force rustfmt-nightly
    #   script: cargo fmt -- --check

sudo: required

before_install:
  - ci/before_install.bash

env:
  global:
    # Default target on travis-ci.
    # Used as conditional check in the install stage
    - HOST=x86_64-unknown-linux-gnu
    # Used on the deployment script
    - PROJECT_NAME=bat

install:
  # prevent target re-add error from rustup
  - if [[ $TRAVIS_OS_NAME = linux && $HOST != $TARGET ]]; then rustup target add $TARGET; fi

script:
  - ci/script.bash

before_deploy:
  - bash ci/before_deploy.bash

deploy:
  provider: releases
  # NOTE updating the `api_key.secure`
  # - go to: https://github.com/settings/tokens/new
  # - generate new token using `public_repo` scope
  # - encrypt it using: `travis encrypt API_KEY_HERE`
  # - paste the output below
  api_key:
    secure: "GZL8nYk+/MZGEqejpP0UMMGVmwUEdxiF9CflFIGccu+/tznDyHnHOpyJmbH3M5IKWIxa7xFgqOE8pHuD4oQXt4mzyJGB0KMKKEFLW7ckbTYKya5bw0heKznaqGnS0Yz+vpfqYR25eiPFYCz7mDsGsCzH5QZqt3cdn9MDQGu1D0IaxPjuBNH3T9kBblR0ifBd3Diy0JYhiVcMSI3V1rkel18F1yBHO2GQ95hkWxVgjANQnefq5+zdzbx4ki539ewYENShA8N0w6ULJVY9LmWRWJIp90ggOD3JCH6Nz/fBpYBqwfABO6Dr7Csw7IRm7pM4IVkFZS1taiDYFX9thxZh3UTmI4YYtSTPxH9NGV47kxPipbf4vAXFwW+1VcHKMpBzz+5IVsHnk1Pfx9/GZsXbqXM3PUWCStec36Xtqh5vQapDKEg9khkEs4b1X7/E73gvpRuQl9gbnyufSVWgDsXIWqM+0aDbqQz1FpLmVxz6eij0cB9vR+8kbNoe/CEPHPO9ZllvAHseukOeHPzSBd8rILORXaaGmQausXz2wWgSsffUSYz4705P8joI0zFCfmkHjL+kA1ZUx5SxLCc1ID7CFFZChhAxHoPL7xc21snswwr8QeheERdwvuzKNaerBGZEw8dmMMx98YkjftIj65DDySr2mRZuufBQeQpmMhFyIT4="
  # for uploading multiple files
  file_glob: true
  # NOTE explanation on each env variable
  # - PROJECT_NAME: name of the project, set on the `env.global` above
  # - TRAVIS_TAG: tag name that the build is being deployed for, usually the version number
  # - TARGET: target triple of the build
  file:
    - $PROJECT_NAME-$TRAVIS_TAG-$TARGET.*
    - $PROJECT_NAME*.deb
  # don't delete artifacts from previous stage
  skip_cleanup: true
  on:
    # deploy only if we push a tag
    tags: true
    # deploy only on stable channel that has TARGET env variable sets
    condition: $TRAVIS_RUST_VERSION = stable && $TARGET != ""

notifications:
  email:
    on_success: never
