%YAML 1.2
---
file_extensions: [yasm, nasm, asm, inc, mac]
scope: source.asm.x86_64

variables:
  num_bin: '(?:[01][01_]*)'
  num_oct: '(?:[0-7][0-7_]*)'
  num_dec: '(?:[0-9][0-9_]*)'
  num_hex: '(?:[[:xdigit:]][[:xdigit:]_]*)'
  num_bin_exp: '(?:p[+-]?{{num_dec}})'
  num_dec_exp: '(?:e[+-]?{{num_dec}})'
  identifier_body: '(?:[[:alnum:]_$#@~.?]*)'
  valid_identifier: '(?:[[:alpha:]_?]{{identifier_body}})'

contexts:
  prototype:
    - include: comments

  line-continuation:
    - match: '(\\)$\n'
      captures:
        1: punctuation.separator.continuation.asm.x86_64
    - match: '\\(\s+?)$'
      captures:
        1: invalid.illegal.space-after-continuation.asm.x86_64
  line-ending:
    - match: '$\n'
      pop: true
  pop:
    - match: '(?=.|$)'
      pop: true
  pop-if-not-whitespace:
    - match: '(?=\S)'
      set: pop

  main:
    - include: comments
    - include: prefixes
    - include: data-types
    - include: registers
    - include: mnemonics
    - include: strings
    - include: numbers
    - include: operators
    - include: support
    - include: directives
    - include: entities
    - include: structs
    - include: preprocessor
    - include: labels

  numbers:
    - include: floating-point
    - include: integers
  floating-point:
    # binary
    - match: '(?i)\b0[by]{{num_bin}}\.(?:{{num_bin}}?{{num_bin_exp}}?\b)?'
      scope: constant.numeric.binary.floating-point.asm.x86_64
    - match: '(?i)\b0[by]{{num_bin}}{{num_bin_exp}}\b'
      scope: constant.numeric.binary.floating-point.asm.x86_64
    # octal
    - match: '(?i)\b0[oq]{{num_oct}}\.(?:{{num_oct}}?{{num_bin_exp}}?\b)?'
      scope: constant.numeric.octal.floating-point.asm.x86_64
    - match: '(?i)\b0[oq]{{num_oct}}{{num_bin_exp}}\b'
      scope: constant.numeric.octal.floating-point.asm.x86_64
    # decimal
    - match: '(?i)\b(?:0[dt])?{{num_dec}}\.(?:{{num_dec}}?{{num_dec_exp}}?\b)?'
      scope: constant.numeric.decimal.floating-point.asm.x86_64
    - match: '(?i)\b{{num_dec}}{{num_dec_exp}}\b'
      scope: constant.numeric.decimal.floating-point.asm.x86_64
    #- match: '(?i)\.{{num_dec}}{{num_dec_exp}}?\b'
    #  scope: invalid.illegal.constant.numeric.decimal.floating-point.asm.x86_64
    - match: '(?i)\b{{num_dec}}p{{num_dec}}?\b'
      scope: constant.numeric.decimal.packed-bcd.asm.x86_64
    # hex
    - match: '(?i)\b0[xh]{{num_hex}}\.(?:{{num_hex}}?{{num_bin_exp}}?\b)?'
      scope: constant.numeric.hex.floating-point.asm.x86_64
    - match: '(?i)\b0[xh]{{num_hex}}{{num_bin_exp}}\b'
      scope: constant.numeric.hex.floating-point.asm.x86_64
    - match: '(?i)\$[0-9]\_?{{num_hex}}?\.(?:{{num_hex}}?{{num_bin_exp}}?\b)?'
      scope: constant.numeric.hex.floating-point.asm.x86_64
    - match: '(?i)\$[0-9]\_?{{num_hex}}{{num_bin_exp}}\b'
      scope: constant.numeric.hex.floating-point.asm.x86_64
  integers:
    - match: '(?i)\b(?:(?:0[by]{{num_bin}})|(?:{{num_bin}}[by]))\b'
      scope: constant.numeric.binary.asm.x86_64
    - match: '(?i)\b(?:(?:0[oq]{{num_oct}})|(?:{{num_oct}}[oq]))\b'
      scope: constant.numeric.octal.asm.x86_64
    - match: '(?i)\b(?:(?:0[dt]{{num_dec}})|(?:{{num_dec}}[dt]?))\b'
      scope: constant.numeric.decimal.asm.x86_64
    - match: '(?i)(?:\$[0-9]\_?{{num_hex}}?)\b'
      scope: constant.numeric.hex.asm.x86_64
    - match: '(?i)\b(?:(?:0[xh]{{num_hex}})|(?:{{num_hex}}[hxHX]))\b'
      scope: constant.numeric.hex.asm.x86_64

  labels:
    # http://www.nasm.us/doc/nasmdoc3.html#section-3.9
    - match: '(\.\.@)({{valid_identifier}})(?:(\:)?|\b)'
      captures:
        1: punctuation.separator.asm.x86_64 storage.modifier.asm.x86_64
        2: entity.name.constant.special.asm.x86_64
        3: punctuation.separator.asm.x86_64
    - match: '(?:(\.)?|\b)({{valid_identifier}})(?:(\:)?|\b)'
      captures:
        1: punctuation.separator.asm.x86_64 storage.modifier.asm.x86_64
        2: entity.name.constant.asm.x86_64
        3: punctuation.separator.asm.x86_64
    - match: '(\.)([0-9]+{{identifier_body}})(?:(\:)?|\b)'
      captures:
        1: punctuation.separator.asm.x86_64 storage.modifier.asm.x86_64
        2: entity.name.constant.asm.x86_64
        3: punctuation.separator.asm.x86_64
    - match: '(?:(\.)?|\b)([0-9$@~]{{identifier_body}})(?:(\:)?|\b)'
      captures:
        1: punctuation.separator.asm.x86_64 storage.modifier.asm.x86_64
        2: invalid.illegal.entity.name.constant.asm.x86_64
        3: punctuation.separator.asm.x86_64
    - match: '((%)(\d+))'
      captures:
        1: invalid.illegal.meta.preprocessor.asm.x86_64
        #1: meta.preprocessor.asm.x86_64
        2: punctuation.definition.variable.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        3: variable.other.preprocessor.asm.x86_64
    - match: '((%%)({{valid_identifier}})(?:(\:)?|\b))'
      captures:
        1: invalid.illegal.meta.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        3: entity.name.constant.special.asm.x86_64
        4: punctuation.separator.asm.x86_64

  data-types:
    # Initialized & uninitialized data: http://www.nasm.us/doc/nasmdoc3.html#section-3.2.1
    - match: '(?i)\b(?:s?byte|(?:[doqtyz]|dq|s[dq]?)?word|(?:d|res)[bdoqtwyz]|ddq)\b'
      scope: storage.type.asm.x86_64
    - match: '(?i)\b(?:incbin|equ|times)\b'
      scope: support.function.asm.x86_64

  prefixes:
    - match: '(?i)\b(?:strict|nosplit|near|far|abs|rel)\b'
      scope: storage.modifier.asm.x86_64
    - match: '(?i)\b(?:[ao](?:16|32|64))\b'
      scope: storage.modifier.prefix.asm.x86_64
    - match: '(?i)\b(?:rep(?:n?[ez])?|lock|xacquire|xrelease|(?:no)?bnd)\b'
      scope: storage.modifier.prefix.asm.x86_64
    - match: '{(vex[23]|evex)}'
      captures:
        1: storage.modifier.prefix.vex.asm.x86_64
    - match: '{(k[1-7])}'
      captures:
        1: storage.modifier.opmask.asm.x86_64
    - match: '{(1to(?:8|16))}'
      captures:
        1: storage.modifier.precision.asm.x86_64
    - match: '{(z|(?:r[nudz]-)?sae)}'
      captures:
        1: storage.modifier.rounding.asm.x86_64

  support:
    # http://www.nasm.us/doc/nasmdoc7.html#section-7.4.6
    - match: '\.\.(?:start|imagebase|tlvp|got(?:pc(?:rel)?|(?:tp)?off)?|plt|sym|tlsie)\b'
      scope: support.constant.asm.x86_64
    - match: '\b__(?:utf(?:(?:16|32)(?:[lb]e)?)|float(?:8|16|32|64|80[me]|128[lh])|Infinity|[QS]?NaN)__\b'
      scope: support.function.asm.x86_64
    # http://www.nasm.us/doc/nasmdoc4.html#section-4.11
    - match: '\b__NASM_(?:MAJOR|(?:SUB)?MINOR|SNAPSHOT|VER(?:SION_ID)?)__\b'
      scope: support.function.asm.x86_64
    - match: '\b___NASM_PATCHLEVEL__\b'
      scope: support.function.asm.x86_64
    - match: '\b__(?:FILE|LINE|BITS|OUTPUT_FORMAT)__\b'
      scope: support.function.asm.x86_64
    - match: '\b__(?:(?:UTC_)?(?:DATE|TIME)(?:_NUM)?|POSIX_TIME)__\b'
      scope: support.function.asm.x86_64
    - match: '\b__USE_(?:ALTREG|SMARTALIGN|FP|IFUNC)__\b'
      scope: support.function.asm.x86_64
    - match: '\b__PASS__\b'
      scope: invalid.deprecated.support.constant.altreg.asm.x86_64
    - match: '(?i)\balignmode\b'
      scope: support.function.smartalign.asm.x86_64
      push:
        - include: line-continuation
        - include: line-ending
        - match: '\b(?:generic|nop|k7|k8|p6)\b'
          scope: support.constant.smartalign.asm.x86_64
          pop: true
        - match: '\w+'
          scope: invalid.illegal.constant.smartalign.asm.x86_64
          pop: true
    - match: '\b__ALIGNMODE__\b'
      scope: support.constant.smartalign.asm.x86_64
    - match: '\b(?:Inf|[QS]?NaN)\b'
      scope: support.constant.fp.asm.x86_64
    - match: '\b(?:float(?:8|16|32|64|80[me]|128[lh]))\b'
      scope: support.function.fp.asm.x86_64
    - match: '(?i)\bilog2(?:[ewfc]|[fc]w)?\b'
      scope: support.function.ifunc.asm.x86_64

  directives:
    - match: '(?i)\b(?:ideal|jumps|p[345]86|end)\b'
      scope: invalid.deprecated.tasm.asm.x86_64
    - match: '(?i)\b(?:bits|use(?:16|32|64)|org|uppercase|safeseh|osabi)\b'
      scope: support.function.directive.asm.x86_64
    - match: '(?i)\b(default)(?:\s+(rel|abs|(?:no)?bnd))?\b'
      captures:
        1: support.function.directive.asm.x86_64
        2: support.constant.directive.asm.x86_64
    - match: '(?i)\b(?:section|segment)\b'
      scope: support.function.directive.asm.x86_64
      push: [section-parameters, section-name]
    - match: '(?i)\bgroup\b'
      scope: support.function.directive.asm.x86_64
      push: section-name
    - match: '\b(?:__SECT__|__NASMDEFSEG|_?_GLOBAL_OFFSET_TABLE_)\b'
      scope: support.constant.directive.asm.x86_64
    - match: '(?i)\b(?:absolute|common)\b'
      scope: support.function.directive.asm.x86_64
    - match: '(?i)\b(?:subsections_via_symbols|no_dead_strip)\b'
      scope: support.function.directive.macho.asm.x86_64
    - match: '(?i)import\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '{{valid_identifier}}'
          scope: entity.name.function.asm.x86_64
          set:
            - match: '{{valid_identifier}}'
              scope: meta.path.asm.x86_64 string.unquoted.asm.x86_64
              set:
                - match: '{{valid_identifier}}'
                  scope: variable.function.asm.x86_64
                  set:
                    - include: comments
                    - match: '{{valid_identifier}}'
                      scope: invalid.illegal.asm.x86_64
                    - include: line-continuation
                    - include: pop-if-not-whitespace
                    - include: line-ending
                - include: line-continuation
                - include: pop-if-not-whitespace
                - include: line-ending
            - include: line-continuation
            - include: pop-if-not-whitespace
            - include: line-ending
        - include: line-continuation
        - include: pop-if-not-whitespace
        - include: line-ending
    - match: '(?i)export\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '{{valid_identifier}}'
          scope: entity.name.constant.asm.x86_64
          set:
            - match: '{{valid_identifier}}'
              scope: variable.function.asm.x86_64
              set: export-parameters
            - include: line-continuation
            - include: pop-if-not-whitespace
            - include: line-ending
        - include: line-continuation
        - include: pop-if-not-whitespace
        - include: line-ending
    - match: '(?i)global\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '(?<=\:)(?:function|data|export)\b'
          scope: storage.type.directive.asm.x86_64
        - match: '\b(?:default|internal|hidden|protected|proc|data)\b'
          scope: storage.modifier.directive.asm.x86_64 variable.parameter.directive.asm.x86_64
        - include: line-continuation
        - include: line-ending
        - include: preprocessor-macro-parameter
        - include: main
    - match: '(?i)extern\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '(?<=\:)(?:import)\b'
          scope: storage.type.directive.asm.x86_64
        - match: '\b(?:proc|data)\b'
          scope: storage.modifier.directive.asm.x86_64 variable.parameter.directive.asm.x86_64
        - include: line-continuation
        - include: line-ending
        - include: preprocessor-macro-parameter
        - include: main
    - match: '(?i)library\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '{{valid_identifier}}'
          scope: meta.path.asm.x86_64 string.unquoted.asm.x86_64
          pop: true
        - include: line-continuation
        - include: line-ending
        - include: pop-if-not-whitespace
    - match: '(?i)module\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '\$?{{valid_identifier}}'
          scope: entity.name.namespace.asm.x86_64
          pop: true
        - include: line-continuation
        - include: line-ending
        - include: pop-if-not-whitespace
    - match: '(?i)\bcpu\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '(?i)\b(?:(?:80|[1-6])86|pentium|ppro|p[234]|katmai|willamette|prescott|(?:x|ia)64)\b'
          scope: support.constant.directive.asm.x86_64
          pop: true
        - include: line-continuation
        - include: pop-if-not-whitespace
        - include: line-ending
        - include: main
    - match: '(?i)\bfloat\b'
      scope: support.function.directive.asm.x86_64
      push:
        - match: '(?i)\b(?:(?:no)?daz|near|up|down|zero|default)\b'
          scope: support.constant.directive.asm.x86_64
          pop: true
        - include: line-continuation
        - include: pop-if-not-whitespace
        - include: line-ending
        - include: main
    - match: '(?i)(\[)\s*(warning)\b'
      captures:
        2: support.function.directive.asm.x86_64
      push:
        - match: '(?i)([+\-*])|(error)\s*(=)\s*'
          captures:
            1: keyword.control.warning.asm.x86_64
            2: support.constant.directive.warning.asm.x86_64
            3: keyword.operator.assignment.asm.x86_64
          push:
            - match: '(?i)\b(other|macro-(?:params|selfref|defaults)|orphan-labels|number-overflow|gnu-elf-extensions|float-(?:overflow|denorm|underflow|toolong)|user|lock|hle|bnd|zext-reloc|ptr|(?:bad|unknown|not-my)-pragma|unknown-warning|all)(?=\]|\s)'
              scope: support.constant.directive.warning.asm.x86_64
              pop: true
            - match: '\S+(?=\])'
              scope: invalid.illegal.support.constant.directive.warning.asm.x86_64
              pop: true
            - match: '\S+'
              scope: invalid.illegal.support.constant.directive.warning.asm.x86_64
              pop: true
        - match: '(?i)\b(?:all|error)\b'
          scope: support.constant.directive.warning.asm.x86_64
        - match: ']'
          pop: true
        - include: line-continuation
        - include: line-ending
    - match: '(?i)(\[)\s*(map)\b'
      captures:
        2: support.function.directive.asm.x86_64
      push:
        - match: '(?i)\b(?:all|brief|sections|segments|symbols)\b'
          scope: support.constant.directive.map.asm.x86_64
        - match: '(?i)\b(?:std(?:out|err))\b'
          scope: meta.path.asm.x86_64 support.constant.directive.map.asm.x86_64
        - match: '{{valid_identifier}}'
          scope: meta.path.asm.x86_64 string.unquoted.asm.x86_64
        - match: ']'
          pop: true
        - include: line-continuation
        - include: line-ending
  section-parameters:
    - match: '='
      scope: keyword.operator.assignment.asm.x86_64
    - match: '(?i)\b(?:(?:v)?start|align|absolute)\b'
      scope: variable.parameter.directive.asm.x86_64
    - match: '(?i)\b(?:(?:prog|no)bits|private|public|common|stack|code|text|data|bss|rdata|info)\b'
      scope: storage.modifier.directive.asm.x86_64 variable.parameter.directive.asm.x86_64
    - match: '(?i)\b(?:mixed|zerofill|no_dead_strip|live_support|strip_static_syms)\b'
      scope: storage.modifier.directive.asm.x86_64 variable.parameter.directive.macho.asm.x86_64
    - match: '(?i)\b(?:(?:no)?(?:alloc|exec|write)|tls)\b'
      scope: storage.modifier.directive.asm.x86_64 variable.parameter.directive.elf.asm.x86_64
    - match: '(?i)\b(?:(?:v)?follows)\b'
      scope: variable.parameter.directive.asm.x86_64
      push:
        - match: '='
          scope: keyword.operator.assignment.asm.x86_64
          set: [pop, section-name]
        - include: line-continuation
        - include: line-ending
    - match: '(?i)\b(?:class|overlay)\b'
      scope: variable.parameter.directive.asm.x86_64
      push:
        - match: '='
          scope: keyword.operator.assignment.asm.x86_64
        - match: '{{valid_identifier}}'
          scope: entity.name.class.asm.x86_64 string.unquoted.asm.x86_64
          pop: true
        - include: line-continuation
        - include: line-ending
    - include: line-continuation
    - include: line-ending
    - include: main
  section-name:
    - match: '(?:\.|\b)(?:text|bss|(?:[rpx]|ro)?data|code)\b'
      scope: entity.name.section.asm.x86_64 string.unquoted.asm.x86_64 support.constant.section.asm.x86_64
      pop: true
    - match: '\b__(?:text|const|data|bss|TEXT|CONST|DATA|BSS)\b'
      scope: entity.name.section.asm.x86_64 string.unquoted.asm.x86_64 support.constant.section.macho.asm.x86_64
      pop: true
    - match: '(?:\.)(?:[lt](?:data|bss)|lrodata|comment)\b'
      scope: entity.name.section.asm.x86_64 string.unquoted.asm.x86_64 support.constant.section.elf.asm.x86_64
      pop: true
    - match: '(?:\.|\b){{valid_identifier}}\b'
      scope: entity.name.section.asm.x86_64 string.unquoted.asm.x86_64
      pop: true
    - match: '(?=`|''|")'
      set:
        - meta_scope: entity.name.section.asm.x86_64
        - include: strings
        - include: pop
    - include: line-continuation
    - include: pop-if-not-whitespace
    - include: line-ending
  export-parameters:
    - match: '='
      scope: keyword.operator.assignment.asm.x86_64
    - match: '(?i)\b(?:resident|nodata)\b'
      scope: storage.modifier.directive.asm.x86_64 variable.parameter.directive.asm.x86_64
    - match: '(?i)\b(?:parm)\b'
      scope: variable.parameter.directive.asm.x86_64
    - include: primitive-directive-end
    - include: line-continuation
    - include: line-ending
    - include: main

  operators:
    - match: '[+\-*/]'
      scope: keyword.operator.arithmetic.asm.x86_64
    - match: '(%%|%)[[:space:]]'
      captures:
        1: keyword.operator.arithmetic.asm.x86_64
    - match: '[|&^~!]|<<|>>'
      scope: keyword.operator.bitwise.asm.x86_64
    - match: '(?i)\b(seg|wrt)\b'
      scope: keyword.operator.word.asm.x86_64
    - match: ','
      scope: operator.separator.asm.x86_64

  structs:
    - match: '(?i)\bstruc\b'
      scope: support.function.asm.x86_64 meta.struct.asm.x86_64
      push:
        - meta_content_scope: meta.struct.asm.x86_64.1
        - match: '{{valid_identifier}}'
          scope: entity.name.struct.asm.x86_64
          set:
            - meta_scope: meta.struct.asm.x86_64
            - match: '(?i)\bendstruc\b'
              scope: support.function.asm.x86_64 meta.struct.asm.x86_64
              pop: true
            - include: main
        - match: '\w'
          scope: invalid.illegal.entity.name.struct.asm.x86_64
    - match: '(?i)\bendstruc\b'
      scope: invalid.illegal.support.function.asm.x86_64 meta.struct.asm.x86_64
    - match: '(?i)\bistruc\b'
      scope: support.function.asm.x86_64 meta.struct.asm.x86_64
      push:
        - meta_content_scope: meta.struct.asm.x86_64.1
        - match: '{{valid_identifier}}'
          scope: variable.other.asm.x86_64
          set:
            - meta_scope: meta.struct.asm.x86_64
            - match: '(?i)\bat\b'
              scope: support.function.asm.x86_64 meta.struct.asm.x86_64
            - match: '(?i)\biend\b'
              scope: support.function.asm.x86_64 meta.struct.asm.x86_64
              pop: true
            - include: main
        - match: '\w'
          scope: invalid.illegal.variable.struct.asm.x86_64
    - match: '(?i)\biend\b'
      scope: invalid.illegal.support.function.asm.x86_64 meta.struct.asm.x86_64
    - match: '(?i)\balignb?\b'
      scope: support.function.asm.x86_64
    - match: '(?i)\b(sectalign)\s+(on|off)?'
      captures:
        1: support.function.asm.x86_64
        2: support.constant.asm.x86_64

  entities:
    - match: '\$@feat\.00\b'
      scope: variable.language.sseh.asm.x86_64
    - match: '(\$)(?:\w+)\b'
      scope: variable.other.asm.x86_64
      captures:
        1: punctuation.definition.variable.asm.x86_64
    - match: '\${3,}'
      scope: invalid.illegal.variable.language.asm.x86_64
    - match: '\$'
      scope: variable.language.asm.x86_64
 
  comments:
    - match: (?:(;)|(?:^|\s)(#)\s).*$
      scope: comment.line.asm.x86_64
      captures:
        1: punctuation.definition.comment.asm.x86_64
        2: punctuation.definition.comment.asm.x86_64
    - match: /\*
      scope: punctuation.definition.comment.asm.x86_64
      push:
        - meta_scope: comment.block.asm.x86_64
        - match: \*/
          scope: punctuation.definition.comment.asm.x86_64
          pop: true

  preprocessor: # http://www.nasm.us/doc/nasmdoc4.html
    - include: preprocessor-macro-multiline
    - include: preprocessor-macro-define
    - include: preprocessor-macro-indirection
    - include: preprocessor-conditions
    - include: preprocessor-other
  # single-line macro
  preprocessor-macro-define:
    - match: '^\s*((%)x?i?define)\b'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.import.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64
        - include: preprocessor-comments
        - match: '(?:{{valid_identifier}})(?=\()'
          scope: entity.name.function.preprocessor.asm.x86_64
          set:
            - match: '\('
              scope: punctuation.section.group.begin.asm.x86_64
              set: preprocessor-macro-params
        - match: '{{valid_identifier}}'
          scope: entity.name.constant.preprocessor.asm.x86_64
          set: preprocessor-macro-definition
        - include: line-continuation
        - include: pop-if-not-whitespace
        - include: line-ending
    - match: '^\s*((%)(?:include|depend))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.import.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-macro-definition
    - match: '^\s*((%)use)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.import.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64 string.unquoted.asm.x86_64
        - include: strings
        - include: line-ending
        - include: preprocessor-macro-indirection
    - match: '^\s*((%)(?:assign|i?deftok|strcat|strlen|substr|pathsearch|push|pop|repl|line|clear))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64
        - include: preprocessor-macro-indirection
        - include: preprocessor-conditions-constant
    - match: '^\s*((%)(?:arg|local))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64
        - match: '({{valid_identifier}})(\:)(?=\w+)'
          captures:
            1: meta.preprocessor.asm.x86_64 variable.parameter.macro.asm.x86_64
            2: punctuation.separator.asm.x86_64
        - match: '\,'
          scope: punctuation.separator.asm.x86_64
        - include: data-types
        - match: '\w+\b'
          scope: invalid.illegal.storage.type.asm.x86_64
        - include: line-continuation
        - include: line-ending
    - match: '^\s*((%)stacksize)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64
        - match: '\b(flat(?:64)?|large|small)\b'
          scope: support.constant.macro.asm.x86_64
        - match: '\w+\b'
          scope: invalid.illegal.storage.type.asm.x86_64
        - include: line-continuation
        - include: line-ending
    - match: '^\s*((%)i?defstr)\s+(?:({{valid_identifier}})|(%%)({{valid_identifier}}))\b'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.import.preprocessor.assign.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
        3: entity.name.constant.preprocessor.asm.x86_64
        4: punctuation.definition.keyword.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        5: entity.name.constant.special.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64 string.unquoted.asm.x86_64
        - include: preprocessor-macro-indirection
        - include: line-ending
    - match: '^\s*((%)(?:warning|error|fatal|pragma))(?=\s|$)'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.preprocessor.assign.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
        3: entity.name.constant.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64 string.unquoted.asm.x86_64
        - include: strings
        - include: preprocessor-macro-indirection
        - include: line-ending
  preprocessor-macro-params-illegal:
    - meta_scope: invalid.illegal.asm.x86_64
    - include: preprocessor-macro-params
  preprocessor-macro-params:
    - meta_scope: meta.preprocessor.macro.parameters.asm.x86_64 meta.group.asm.x86_64
    - match: '{{valid_identifier}}'
      scope: variable.parameter.asm.x86_64
    - match: '\)'
      scope: punctuation.section.group.end.asm.x86_64
      set: preprocessor-macro-definition
    - match: ','
      scope: punctuation.separator.asm.x86_64
      push:
        - match: '{{valid_identifier}}'
          scope: variable.parameter.asm.x86_64
          pop: true
        - include: line-continuation
        - include: preprocessor-comments
        - match: '\.\.\.'
          scope: keyword.operator.variadic.asm.x86_64
        - match: '(?=\))'
          pop: true
        - match: '(/\*).*(\*/)'
          scope: comment.block.asm.x86_64
          captures:
            1: punctuation.definition.comment.asm.x86_64
            2: punctuation.definition.comment.asm.x86_64
        - match: '\S+'
          scope: invalid.illegal.unexpected-character.asm.x86_64
    - include: line-continuation
    - include: preprocessor-comments
    - match: '\.\.\.'
      scope: keyword.operator.variadic.asm.x86_64
    - match: '(/\*).*(\*/)'
      scope: comment.block.asm.x86_64
      captures:
        1: punctuation.definition.comment.asm.x86_64
        2: punctuation.definition.comment.asm.x86_64
    - match: '$\n'
      scope: invalid.illegal.unexpected-end-of-line.asm.x86_64
  preprocessor-macro-definition:
    - meta_content_scope: meta.preprocessor.macro.asm.x86_64
    - include: preprocessor-comments
    - include: line-continuation
    - include: line-ending
    - include: main
  preprocessor-macro-indirection:
    - match: '((%)(\[))'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
        3: meta.brackets.asm.x86_64 punctuation.section.brackets.begin.asm.x86_64
      push:
        - meta_scope: meta.brackets.asm.x86_64 meta.preprocessor.macro.asm.x86_64
        - include: line-continuation
        - include: line-ending
        - include: main
        - match: '\]'
          scope: punctuation.section.brackets.end.asm.x86_64 keyword.control.preprocessor.asm.x86_64
          pop: true
    - match: '((%)\+)\s+'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.operator.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
    - match: '((%)(\?\??))'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.operator.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
        3: variable.language.preprocessor.asm.x86_64
    - match: '((%\$+)({{valid_identifier}})(?:(\:)?|\b))'
      captures:
        1: meta.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        3: entity.name.constant.special.asm.x86_64
        4: punctuation.separator.asm.x86_64
    - match: '((%)(\!))'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.operator.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
        3: punctuation.definition.variable.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.asm.x86_64 keyword.operator.preprocessor.asm.x86_64 variable.parameter.preprocessor.environment.asm.x86_64
        - match: '{{valid_identifier}}'
          scope: variable.parameter.preprocessor.environment.unquoted.asm.x86_64
          pop: true
        - match: '(?=`|''|")'
          set:
            - meta_scope: variable.parameter.preprocessor.environment.quoted.asm.x86_64
            - include: strings
            - include: pop
        - include: pop
  preprocessor-conditions: # also see preprocessor-macro-conditions-multiline
    - match: '^\s*((%)ifn?(?:idni?|id|num|str|token|empty|env)?)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-conditions-content-block
    - match: '^\s*((%)ifn?(?:def|ctx))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: [preprocessor-conditions-content-block, preprocessor-conditions-constant]
    - match: '^\s*((%)rep)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-conditions-content-block
    - match: '^\s*((%)ifn?macro)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: [preprocessor-conditions-content-block, preprocessor-macro-arguments-signature]
    - include: preprocessor-conditions-parity
  preprocessor-conditions-constant:
    - meta_content_scope: meta.preprocessor.asm.x86_64
    - match: '(?:{{valid_identifier}})(?=\()'
      scope: entity.name.constant.preprocessor.asm.x86_64
      set:
        - match: '\('
          scope: punctuation.section.group.begin.asm.x86_64
          set: preprocessor-macro-params-illegal
    - match: '{{valid_identifier}}\b'
      scope: meta.preprocessor.asm.x86_64 entity.name.constant.preprocessor.asm.x86_64
      pop: true
    - include: line-continuation
    - include: pop-if-not-whitespace
    - include: line-ending
    - include: main
  preprocessor-conditions-content-block:
    - meta_content_scope: meta.block.preprocessor.asm.x86_64
    - include: preprocessor-conditions-content-block-common
    - include: main
  preprocessor-conditions-content-block-common:
    - match: '^\s*((%)(?:elifn?(?:idni?|id|num|str|token|empty|env)?|else|exitrep))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
    - match: '^\s*((%)(?:elifn?(?:def|ctx)))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-conditions-constant
    - match: '^\s*((%)elifn?macro)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-macro-arguments-signature
    - match: '^\s*((%)(?:end(?:if|rep)))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      pop: true
  preprocessor-conditions-parity:
    - match: '^\s*((%)(?:elifn?(?:macro|def|ctx|idni?|id|num|str|token|empty|env)?|else|(?:end(?:if|rep))|exitrep))\b'
      captures:
        1: invalid.illegal.meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
  preprocessor-other:
    - include: preprocessor-macro-indirection
    - match: '^\s*((%)undef)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_scope: meta.preprocessor.asm.x86_64
        - include: line-continuation
        - include: line-ending
        - include: preprocessor-comments
        - match: '{{valid_identifier}}'
          scope: entity.name.constant.preprocessor.asm.x86_64
          pop: true
    - match: '^\s*((%)unmacro)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.import.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-macro-arguments-signature
  preprocessor-macro-arguments-signature:
    - meta_scope: meta.preprocessor.asm.x86_64
    - include: preprocessor-comments
    - match: '({{valid_identifier}})(?:\s+(?:(\d+(?:(-)(?:\d+|\*))?)(\+)?(\.nolist\b)?))'
      captures:
        1: entity.name.function.preprocessor.asm.x86_64
        2: variable.parameter.preprocessor.asm.x86_64
        3: keyword.operator.preprocessor.range.asm.x86_64
        4: storage.modifier.preprocessor.greedy.asm.x86_64
        5: storage.modifier.preprocessor.listing.asm.x86_64
      pop: true
    - include: line-continuation
    - include: pop-if-not-whitespace
    - include: line-ending
  # multi-line macro
  preprocessor-macro-multiline:
    - match: '^\s*((%)i?macro)\b'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.import.define.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64
        - include: preprocessor-comments
        - match: '(?:{{valid_identifier}})(?=\()'
          scope: entity.name.constant.preprocessor.asm.x86_64
          set:
            - meta_content_scope: invalid.illegal.asm.x86_64
            - match: '\('
              scope: punctuation.section.group.begin.asm.x86_64
              set: preprocessor-macro-params-illegal
        - match: '({{valid_identifier}})(?:\s+(?:(\d+(?:(-)(?:\d+|\*))?)(\+)?(\.nolist\b)?))'
          captures:
            1: entity.name.function.preprocessor.asm.x86_64
            2: variable.parameter.preprocessor.asm.x86_64
            3: keyword.operator.preprocessor.range.asm.x86_64
            4: storage.modifier.preprocessor.greedy.asm.x86_64
            5: storage.modifier.preprocessor.listing.asm.x86_64
          set: preprocessor-macro-definition-multiline-with-parameters
    - match: '^\s*((%)endmacro)\b'
      captures:
        1: invalid.illegal.meta.preprocessor.asm.x86_64 keyword.control.import.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
  preprocessor-macro-parameter:
    - match: '((%)(\-?\d+))'
      captures:
        1: meta.preprocessor.asm.x86_64
        2: punctuation.definition.variable.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        3: variable.other.preprocessor.asm.x86_64
  preprocessor-macro-definition-multiline-with-parameters:
    - meta_content_scope: meta.preprocessor.macro.asm.x86_64
    - include: preprocessor-comments
    - include: preprocessor-macro-parameter
    - include: preprocessor-macro-indirection
    - include: preprocessor-macro-conditions-multiline
    - include: preprocessor-macro-definition-multiline
  preprocessor-macro-definition-multiline:
    - meta_content_scope: meta.preprocessor.macro.asm.x86_64
    - include: preprocessor-comments
    - match: '^\s*((%)endmacro)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.import.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      pop: true
    - match: '((%%)({{valid_identifier}})(?:(\:)?|\b))'
      captures:
        1: meta.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        3: entity.name.constant.special.asm.x86_64
        4: punctuation.separator.asm.x86_64
    - match: '((%)({))'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
        3: meta.braces.asm.x86_64 punctuation.section.braces.begin.asm.x86_64
      push:
        - meta_scope: meta.braces.asm.x86_64 meta.preprocessor.macro.asm.x86_64
        - include: preprocessor-macro-indirection
        - match: ':'
          scope: punctuation.separator.asm.x86_64
        - match: '}'
          scope: punctuation.section.braces.end.asm.x86_64 keyword.control.preprocessor.asm.x86_64
          pop: true
    - match: '^\s*((%)(?:rotate))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.operator.word.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
    - match: '^\s*((%)(?:include|depend))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.import.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      set: preprocessor-macro-definition-multiline-with-parameters
    - match: '^\s*((%)(?:pathsearch))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      set: [preprocessor-macro-definition-multiline-with-parameters, preprocessor-conditions-constant]
    - match: '^\s*((%)x?i?define)\b'
      captures:
        1: meta.preprocessor.macro.asm.x86_64 keyword.control.import.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push:
        - meta_content_scope: meta.preprocessor.macro.asm.x86_64
        - include: preprocessor-comments
        - match: '(?:{{valid_identifier}})(?=\()'
          scope: entity.name.function.preprocessor.asm.x86_64
          set:
            - match: '\('
              scope: punctuation.section.group.begin.asm.x86_64
              set: preprocessor-macro-params
        - match: '{{valid_identifier}}'
          scope: entity.name.constant.preprocessor.asm.x86_64
          set: preprocessor-macro-definition-inside-multiline
        - include: line-continuation
        - include: pop-if-not-whitespace
        - include: line-ending

    - include: preprocessor-macro-indirection
    - include: preprocessor-conditions
    - include: main
  preprocessor-macro-conditions-multiline: # also see preprocessor-conditions
    - match: '^\s*((%)ifn?(?:idni?|id|num|str|token|empty|env)?)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-conditions-content-block-multiline
    - match: '^\s*((%)ifn?(?:def|ctx))\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: [preprocessor-conditions-content-block-multiline, preprocessor-conditions-constant]
    - match: '^\s*((%)rep)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: preprocessor-conditions-content-block-multiline
    - match: '^\s*((%)ifn?macro)\b'
      captures:
        1: meta.preprocessor.asm.x86_64 keyword.control.preprocessor.asm.x86_64
        2: punctuation.definition.keyword.preprocessor.asm.x86_64
      push: [preprocessor-macro-arguments-signature, preprocessor-conditions-content-block-multiline]
    - include: preprocessor-conditions-parity
  preprocessor-conditions-content-block-multiline:
    - meta_content_scope: meta.block.preprocessor.asm.x86_64
    - include: preprocessor-conditions-content-block-common
    - include: preprocessor-macro-definition-multiline-with-parameters
  preprocessor-macro-definition-inside-multiline:
    - meta_content_scope: meta.preprocessor.macro.asm.x86_64
    - include: preprocessor-macro-parameter
    - include: preprocessor-macro-definition

  strings: # Character strings: http://www.nasm.us/doc/nasmdoc3.html#section-3.4.2
    - match: '`'
      scope: punctuation.definition.string.begin.asm.x86_64
      push: string-backquote
    - match: ''''
      scope: punctuation.definition.string.begin.asm.x86_64
      push: string-single
    - match: '"'
      scope: punctuation.definition.string.begin.asm.x86_64
      push: string-double
  string-content:
    - match: '\\u[[:xdigit:]]{4}'
      scope: constant.character.escape.asm.x86_64
    - match: '\\U[[:xdigit:]]{8}'
      scope: constant.character.escape.asm.x86_64
    - match: '\\x[[:xdigit:]]{1,2}'
      scope: constant.character.escape.asm.x86_64
    - match: '\\[0-7]{1,3}'
      scope: constant.character.escape.asm.x86_64
    - match: '\\[''"`\\?abtnvfre]'
      scope: constant.character.escape.asm.x86_64
    - match: '\\.'
      scope: invalid.illegal.constant.character.escape.asm.x86_64
  string-backquote:
    - meta_scope: string.quoted.other.asm.x86_64
    - meta_include_prototype: false
    - include: string-content
    - match: '`'
      scope: punctuation.definition.string.end
      pop: true
  string-single:
    - meta_scope: string.quoted.single.asm.x86_64
    - meta_include_prototype: false
    - match: ''''
      scope: punctuation.definition.string.end
      pop: true
  string-double:
    - meta_scope: string.quoted.double.asm.x86_64
    - meta_include_prototype: false
    - match: '"'
      scope: punctuation.definition.string.end
      pop: true

  registers:
    - match: '(?i)\b(?:[abcd][hl]|[er]?[abcd]x|[er]?(?:di|si|bp|sp)|dil|sil|bpl|spl|r(?:8|9|1[0-5])[bdlw]?)\b'
      scope: constant.language.register.general-purpose.asm.x86_64
    - match: '(?i)\b(?:[cdefgs]s)\b'
      scope: constant.language.register.segment.asm.x86_64
    - match: '(?i)\b(?:[er]?flags)\b'
      scope: constant.language.register.flags.asm.x86_64
    - match: '(?i)\b(?:[er]?ip)\b'
      scope: constant.language.register.instruction-pointer.asm.x86_64
    - match: '(?i)\b(?:cr[02-4])\b'
      scope: constant.language.register.control.asm.x86_64
    - match: '(?i)\b(?:(?:mm|st|fpr)[0-7])\b'
      scope: constant.language.register.mmx.asm.x86_64
    - match: '(?i)\b(?:[xy]mm(?:[0-9]|1[0-5])|mxcsr)\b'
      scope: constant.language.register.sse_avx.asm.x86_64
    - match: '(?i)\b(?:zmm(?:[12]?[0-9]|30|31))\b'
      scope: constant.language.register.avx512.asm.x86_64
    - match: '(?i)\b(?:bnd(?:[0-3]|cfg[su]|status))\b'
      scope: constant.language.register.memory-protection.asm.x86_64
    - match: '(?i)\b(?:(?:[gil]dt)r?|tr)\b'
      scope: constant.language.register.system-table-pointer.asm.x86_64
    - match: '(?i)\b(?:dr[0-367])\b'
      scope: constant.language.register.debug.asm.x86_64
    - match: '(?i)\b(?:cr8|dr(?:[89]|1[0-5])|efer|tpr|syscfg)\b'
      scope: constant.language.register.amd.asm.x86_64
    - match: '(?i)\b(?:db[0-367]|t[67]|tr[3-7]|st)\b'
      scope: invalid.deprecated.constant.language.register.asm.x86_64
    - match: '(?i)\b[xy]mm(?:1[6-9]|2[0-9]|3[01])\b'
      scope: invalid.deprecated.constant.language.register.asm.x86_64

  mnemonics: # see valid.asm for references
    - include: mnemonics-general-purpose
    - include: mnemonics-fpu
    - include: mnemonics-mmx
    - include: mnemonics-sse
    - include: mnemonics-sse2
    - include: mnemonics-sse3
    - include: mnemonics-sse4
    - include: mnemonics-aesni
    - include: mnemonics-avx
    - include: mnemonics-avx2
    - include: mnemonics-tsx
    - include: mnemonics-system
    - include: mnemonics-64bit
    - include: mnemonics-vmx
    - include: mnemonics-smx
    - include: mnemonics-intel-isa-sgx
    - include: mnemonics-intel-isa-mpx
    - include: mnemonics-intel-isa-sha
    - include: mnemonics-supplemental-amd
    - include: mnemonics-supplemental-cyrix
    - include: mnemonics-supplemental-via
    - include: mnemonics-undocumented
    - include: mnemonics-future-intel
    - include: mnemonics-pseudo-ops
    #- include: mnemonics-invalid
  mnemonics-general-purpose:
    - match: '(?i)\b(?:mov(?:[sz]x)?|cmov(?:n?[abceglopsz]|n?[abgl]e|p[eo]))\b'
      scope: keyword.operator.word.mnemonic.general-purpose.data-transfer.mov
    - match: '(?i)\b(xchg|bswap|xadd|cmpxchg(8b)?)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.data-transfer.xchg
    - match: '(?i)\b((push|pop)(ad?)?|cwde?|cdq|cbw)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.data-transfer.other
    - match: '(?i)\b(adcx?|adox|add|sub|sbb|i?mul|i?div|inc|dec|neg|cmp)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.binary-arithmetic
    - match: '(?i)\b(daa|das|aaa|aas|aam|aad)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.decimal-arithmetic
    - match: '(?i)\b(and|x?or|not)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.logical
    - match: '(?i)\b(s[ah][rl]|sh[rl]d|r[co][rl])\b'
      scope: keyword.operator.word.mnemonic.general-purpose.rotate
    - match: '(?i)\b(set(n?[abceglopsz]|n?[abgl]e|p[eo]))\b'
      scope: keyword.operator.word.mnemonic.general-purpose.bit-and-byte.set
    - match: '(?i)\b(bt[crs]?|bs[fr]|test|crc32|popcnt)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.bit-and-byte.other
    - match: '(?i)\b(jmp|jn?[abceglopsz]|jn?[abgl]e|jp[eo]|j[er]?cxz)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.control-transfer.jmp
    - match: '(?i)\b(loop(n?[ez])?|call|ret|iret[dq]?|into?|bound|enter|leave)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.control-transfer.other
    - match: '(?i)\b((mov|cmp|sca|lod|sto)(s[bdw]?)|rep(n?[ez])?)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.strings
    - match: '(?i)\b((in|out)(s[bdw]?)?)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.io
    - match: '(?i)\b((st|cl)[cdi]|cmc|[ls]ahf|(push|pop)f[dq]?)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.flag-control
    - match: '(?i)\b(l[defgs]s)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.segment-registers
    - match: '(?i)\b(lea|nop|ud2|xlatb?|cpuid|movbe)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.misc
    - match: '(?i)\b(rdrand|rdseed)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.rng
    - match: '(?i)\b(andn|bextr|bls(i|r|msk)|bzhi|pdep|pext|[lt]zcnt|(mul|ror|sar|shl|shr)x)\b'
      scope: keyword.operator.word.mnemonic.general-purpose.bmi
  mnemonics-fpu:
    - match: '(?i)\b(fcmov(n?([beu]|be)))\b'
      scope: keyword.operator.word.mnemonic.fpu.data-transfer.mov
    - match: '(?i)\b(f(i?(ld|stp?)|b(ld|stp)|xch))\b'
      scope: keyword.operator.word.mnemonic.fpu.data-transfer.other
    - match: '(?i)\b(f((add|div|mul|sub)p?|i(add|div|mul|sub)|(div|sub)rp?|i(div|sub)r))\b'
      scope: keyword.operator.word.mnemonic.fpu.basic-arithmetic.basic
    - match: '(?i)\b(f(prem1?|abs|chs|rndint|scale|sqrt|xtract))\b'
      scope: keyword.operator.word.mnemonic.fpu.basic-arithmetic.other
    - match: '(?i)\b(f(u?com[ip]?p?|icomp?|tst|xam))\b'
      scope: keyword.operator.word.mnemonic.fpu.comparison
    - match: '(?i)\b(f(sin|cos|sincos|pa?tan|2xm1|yl2x(p1)?))\b'
      scope: keyword.operator.word.mnemonic.fpu.transcendental
    - match: '(?i)\b(fld(1|z|pi|l2[et]|l[ng]2))\b'
      scope: keyword.operator.word.mnemonic.fpu.load-constants
    - match: '(?i)\b(f((inc|dec)stp|free|n?(init|clex|st[cs]w|stenv|save)|ld(cw|env)|rstor|nop)|f?wait)\b'
      scope: keyword.operator.word.mnemonic.fpu.control-management
    - match: '(?i)\b(fx(save|rstor)(64)?)\b'
      scope: keyword.operator.word.mnemonic.fpu.state-management
  mnemonics-mmx:
    - match: '(?i)\b(mov[dq])\b'
      scope: keyword.operator.word.mnemonic.mmx.data-transfer
    - match: '(?i)\b(pack(ssdw|[su]swb)|punpck[hl](bw|dq|wd))\b'
      scope: keyword.operator.word.mnemonic.mmx.conversion
    - match: '(?i)\b(p(((add|sub)(d|(u?s)?[bw]))|maddwd|mul[lh]w))\b'
      scope: keyword.operator.word.mnemonic.mmx.packed-arithmetic
    - match: '(?i)\b(pcmp((eq|gt)[bdw]))\b'
      scope: keyword.operator.word.mnemonic.mmx.comparison
    - match: '(?i)\b(pandn?|px?or)\b'
      scope: keyword.operator.word.mnemonic.mmx.logical
    - match: '(?i)\b(ps([rl]l[dwq]|raw|rad))\b'
      scope: keyword.operator.word.mnemonic.mmx.shift-and-rotate
    - match: '(?i)\b(emms)\b'
      scope: keyword.operator.word.mnemonic.mmx.state-management
  mnemonics-sse:
    - match: '(?i)\b(mov(([ahlu]|hl|lh|msk)ps|ss))\b'
      scope: keyword.operator.word.mnemonic.sse.data-transfer
    - match: '(?i)\b((add|div|max|min|mul|rcp|r?sqrt|sub)[ps]s)\b'
      scope: keyword.operator.word.mnemonic.sse.packed-arithmetic
    - match: '(?i)\b(cmp[ps]s|u?comiss)\b'
      scope: keyword.operator.word.mnemonic.sse.comparison
    - match: '(?i)\b((andn?|x?or)ps)\b'
      scope: keyword.operator.word.mnemonic.sse.logical
    - match: '(?i)\b((shuf|unpck[hl])ps)\b'
      scope: keyword.operator.word.mnemonic.sse.shuffle-and-unpack
    - match: '(?i)\b(cvt(pi2ps|si2ss|ps2pi|tps2pi|ss2si|tss2si))\b'
      scope: keyword.operator.word.mnemonic.sse.conversion
    - match: '(?i)\b((ld|st)mxcsr)\b'
      scope: keyword.operator.word.mnemonic.sse.state-management
    - match: '(?i)\b(p(avg[bw]|extrw|insrw|(max|min)(sw|ub)|sadbw|shufw|mulhuw|movmskb))\b'
      scope: keyword.operator.word.mnemonic.sse.simd-integer
    - match: '(?i)\b(maskmovq|movntps|sfence)\b'
      scope: keyword.operator.word.mnemonic.sse.cacheability-control
    - match: '(?i)\b(prefetch(nta|t[0-2]|w(t1)?))\b'
      scope: keyword.operator.word.mnemonic.sse.prefetch
  mnemonics-sse2:
    - match: '(?i)\b(mov([auhl]|msk)pd)\b'
      scope: keyword.operator.word.mnemonic.sse2.data-transfer
    - match: '(?i)\b((add|div|max|min|mul|sub|sqrt)[ps]d)\b'
      scope: keyword.operator.word.mnemonic.sse2.packed-arithmetic
    - match: '(?i)\b((andn?|x?or)pd)\b'
      scope: keyword.operator.word.mnemonic.sse2.logical
    - match: '(?i)\b((cmpp|u?comis)d)\b'
      scope: keyword.operator.word.mnemonic.sse2.compare
    - match: '(?i)\b((shuf|unpck[hl])pd)\b'
      scope: keyword.operator.word.mnemonic.sse2.shuffle-and-unpack
    - match: '(?i)\b(cvt(dq2pd|pi2pd|ps2pd|pd2ps|si2sd|sd2ss|ss2sd|t?(pd2dq|pd2pi|sd2si)))\b'
      scope: keyword.operator.word.mnemonic.sse2.conversion
    - match: '(?i)\b(cvt(dq2ps|ps2dq|tps2dq))\b'
      scope: keyword.operator.word.mnemonic.sse2.packed-floating-point
    - match: '(?i)\b(mov(dq[au]|q2dq|dq2q))\b'
      scope: keyword.operator.word.mnemonic.sse2.simd-integer.mov
    - match: '(?i)\b(p((add|sub|(s[lr]l|mulu|unpck[hl]q)d)q|shuf(d|[hl]w)))\b'
      scope: keyword.operator.word.mnemonic.sse2.simd-integer.other
    - match: '(?i)\b(clflush|[lm]fence|pause|maskmovdqu|movnt(dq|i|pd))\b'
      scope: keyword.operator.word.mnemonic.sse2.cacheability-control
  mnemonics-sse3:
    - match: '(?i)\b(fisttp|lddqu|(addsub|h(add|sub))p[sd]|mov(sh|sl|d)dup|monitor|mwait)\b'
      scope: keyword.operator.word.mnemonic.sse3
    - match: '(?i)\b(ph(add|sub)(s?w|d))\b'
      scope: keyword.operator.word.mnemonic.sse3.supplimental.horizontal-packed-arithmetic
    - match: '(?i)\b(p((abs|sign)[bdw]|maddubsw|mulhrsw|shufb|alignr))\b'
      scope: keyword.operator.word.mnemonic.sse3.supplimental.other
  mnemonics-sse4:
    - match: '(?i)\b(pmul(ld|dq)|dpp[ds])\b'
      scope: keyword.operator.word.mnemonic.sse4.1.arithmetic
    - match: '(?i)\b(movntdqa)\b'
      scope: keyword.operator.word.mnemonic.sse4.1.load-hint
    - match: '(?i)\b(blendv?p[ds]|pblend(vb|w))\b'
      scope: keyword.operator.word.mnemonic.sse4.1.packed-blending
    - match: '(?i)\b(p(min|max)(u[dw]|s[bd]))\b'
      scope: keyword.operator.word.mnemonic.sse4.1.packed-integer
    - match: '(?i)\b(round[ps][sd])\b'
      scope: keyword.operator.word.mnemonic.sse4.1.packed-floating-point
    - match: '(?i)\b((extract|insert)ps|p((ins|ext)(r[bdq])))\b'
      scope: keyword.operator.word.mnemonic.sse4.1.insertion-and-extraction
    - match: '(?i)\b(pmov([sz]x(b[dqw]|dq|wd|wq)))\b'
      scope: keyword.operator.word.mnemonic.sse4.1.conversion
    - match: '(?i)\b(mpsadbw|phminposuw|ptest|pcmpeqq|packusdw)\b'
      scope: keyword.operator.word.mnemonic.sse4.1.other
    - match: '(?i)\b(pcmp([ei]str[im]|gtq))\b'
      scope: keyword.operator.word.mnemonic.sse4.2
  mnemonics-aesni:
    - match: '(?i)\b(aes((dec|enc)(last)?|imc|keygenassist)|pclmulqdq)\b'
      scope: keyword.operator.word.mnemonic.aesni
  mnemonics-avx:
    - match: '(?i)\b(v((test|permil|maskmov)p[ds]|zero(all|upper)|(perm2|insert|extract|broadcast)f128|broadcasts[ds]))\b'
      scope: keyword.operator.word.mnemonic.avx
    - match: '(?i)\b(vaes((dec|enc)(last)?|imc|keygenassist)|vpclmulqdq)\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.aes
    - match: '(?i)\b(v((cmp[ps]|u?comis)[ds]|pcmp([ei]str[im]|(eq|gt)[bdqw])))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.comparison
    - match: '(?i)\b(v(cvt(dq2pd|dq2ps|pd2ps|ps2pd|sd2ss|si2sd|si2ss|ss2sd|t?(pd2dq|ps2dq|sd2si|ss2si))))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.conversion
    - match: '(?i)\b(vh((add|sub)p[ds])|vph((add|sub)([dw]|sw)|minposuw))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.horizontal-packed-arithmetic
    - match: '(?i)\b(v((andn?|x?or)p[ds]))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.logical
    - match: '(?i)\b(v(mov(([ahl]|msk|nt|u)p[ds]|(hl|lh)ps|s([ds]|[hl]dup)|q)))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.mov
    - match: '(?i)\b(v((add|div|mul|sub|max|min|round|sqrt)[ps][ds]|(addsub|dp)p[ds]|(rcp|rsqrt)[ps]s))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.packed-arithmetic
    - match: '(?i)\b(v(pack[su]s(dw|wb)|punpck[hl](bw|dq|wd|qdq)|unpck[hl]p[ds]))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.packed-conversion
    - match: '(?i)\b(vp(shuf([bd]|[hl]w))|vshufp[ds])\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.packed-shuffle
    - match: '(?i)\b(vp((abs|sign|(max|min)[su])[bdw]|(add|sub)([bdqw]|u?s[bw])|avg[bw]|extr[bdqw]|madd(wd|ubsw)|mul(hu?w|hrsw|l[dw]|u?dq)|sadbw))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.supplemental.arithmetic
    - match: '(?i)\b(vp(andn?|x?or))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.supplemental.logical
    - match: '(?i)\b(vpblend(vb|w))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.supplemental.blending
    - match: '(?i)\b(vpmov(mskb|[sz]x(b[dqw]|w[dq]|dq)))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.supplemental.mov
    - match: '(?i)\b(vp(insr[bdqw]|sll(dq|[dqw])|srl(dq)))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.simd-integer
    - match: '(?i)\b(vp(sra[dwq]|srl[dqw]))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.shift-and-rotate
    - match: '(?i)\b(vblendv?p[ds])\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.packed-blending
    - match: '(?i)\b(vp(test|alignr))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.packed-other
    - match: '(?i)\b(vmov(d(dup|qa|qu)?))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.simd-integer.mov
    - match: '(?i)\b(v((extract|insert)ps|lddqu|(ld|st)mxcsr|mpsadbw))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.other
    - match: '(?i)\b(v(maskmovdqu|movntdqa?))\b'
      scope: keyword.operator.word.mnemonic.avx.promoted.cacheability-control
    - match: '(?i)\b(vcvt(ph2ps|ps2ph))\b'
      scope: keyword.operator.word.mnemonic.16-bit-floating-point-conversion
    - match: '(?i)\b(vfn?m((add|sub)(132|213|231)[ps][ds])|vfm((addsub|subadd)(132|213|231)p[ds]))\b'
      scope: keyword.operator.word.mnemonic.fma
  mnemonics-avx2:
    - match: '(?i)\b(v((broadcast|extract|insert|perm2)i128|pmaskmov[dq]|perm([dsq]|p[sd])))\b'
      scope: keyword.operator.word.mnemonic.avx2.promoted.simd
    - match: '(?i)\b(vpbroadcast[bdqw])\b'
      scope: keyword.operator.word.mnemonic.avx2.promoted.packed
    - match: '(?i)\b(vp(blendd|s[lr]lv[dq]|sravd))\b'
      scope: keyword.operator.word.mnemonic.avx2.blend
    - match: '(?i)\b(vp?gather[dq][dq]|vgather([dq]|dq)p[ds])\b'
      scope: keyword.operator.word.mnemonic.avx2.gather
  mnemonics-tsx:
    - match: '(?i)\b(x(abort|acquire|release|begin|end|test))\b'
      scope: keyword.operator.word.mnemonic.tsx
  mnemonics-system:
    - match: '(?i)\b((cl|st)ac|[ls]([gli]dt|tr|msw)|clts|arpl|lar|lsl|ver[rw]|inv(d|lpg|pcid)|wbinvd)\b'
      scope: keyword.operator.word.mnemonic.system
    - match: '(?i)\b(lock|hlt|rsm|(rd|wr)(msr|pkru|[fg]sbase)|rd(pmc|tscp?)|sys(enter|exit))\b'
      scope: keyword.operator.word.mnemonic.system
    - match: '(?i)\b(x((save(c|opt|s)?|rstors?)(64)?|[gs]etbv))\b'
      scope: keyword.operator.word.mnemonic.system
  mnemonics-64bit:
    - match: '(?i)\b(cdqe|cqo|(cmp|lod|mov|sto)sq|cmpxchg16b|mov(ntq|sxd)|scasq|swapgs|sys(call|ret))\b'
      scope: keyword.operator.word.mnemonic.64-bit-mode
  mnemonics-vmx:
    - match: '(?i)\b(vm(ptr(ld|st)|clear|read|write|launch|resume|xo(ff|n)|call|func)|inv(ept|vpid))\b'
      scope: keyword.operator.word.mnemonic.vmx
  mnemonics-smx:
    - match: '(?i)\b(getsec)\b'
      scope: keyword.operator.word.mnemonic.smx.getsec
    - match: '(?i)\b(capabilities|enteraccs|exitac|senter|sexit|parameters|smctrl|wakeup)\b'
      scope: support.constant
  mnemonics-intel-isa-sgx:
    - match: '(?i)\be(add|block|create|dbg(rd|wr)|extend|init|ld[bu]|pa|remove|track|wb)\b'
      scope: keyword.operator.word.mnemonic.sgx1.supervisor
    - match: '(?i)\be(enter|exit|getkey|report|resume)\b'
      scope: keyword.operator.word.mnemonic.sgx1.user
    - match: '(?i)\be(aug|mod(pr|t))\b'
      scope: keyword.operator.word.mnemonic.sgx2.supervisor
    - match: '(?i)\be(accept(copy)?|modpe)\b'
      scope: keyword.operator.word.mnemonic.sgx2.user
  mnemonics-intel-isa-mpx:
    - match: '(?i)\b(bnd(mk|c[lnu]|mov|ldx|stx))\b'
      scope: keyword.operator.word.mnemonic.mpx
  mnemonics-intel-isa-sha:
    - match: '(?i)\b(sha(1rnds4|256rnds2|1nexte|(1|256)msg[12]))\b'
      scope: keyword.operator.word.mnemonic.sha
  mnemonics-supplemental-amd:
    - match: '(?i)\b(bl([cs](fill|ic?|msk)|cs)|t1mskc|tzmsk)\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.general-purpose
    - match: '(?i)\b(clgi|int3|invlpga|iretw|skinit|stgi|vm(load|mcall|run|save)|monitorx|mwaitx)\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.system
    - match: '(?i)\b([ls]lwpcb|lwp(ins|val))\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.profiling
    - match: '(?i)\b(movnts[ds])\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.memory-management
    - match: '(?i)\b(prefetch|clzero)\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.cache-management
    - match: '(?i)\b((extr|insert)q)\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.sse4.a
    - match: '(?i)\b(vfn?m((add|sub)[ps][ds])|vfm((addsub|subadd)p[ds]))\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.fma4
    - match: '(?i)\b(vp(cmov|(comu?|rot|sh[al])[bdqw]|mac(s?s(d(d|q[hl])|w[dw]))|madcss?wd|perm))\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.xop.simd
    - match: '(?i)\b(vph(addu?(b[dqw]|w[dq]|dq)|sub(bw|dq|wd)))\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.xop.simd-horizontal
    - match: '(?i)\b(vfrcz[ps][ds]|vpermil2p[ds])\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.xop.other
    - match: '(?i)\b(femms)\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.3dnow
    - match: '(?i)\b(p(avgusb|(f2i|i2f)[dw]|mulhrw|swapd)|pf((p?n)?acc|add|max|min|mul|rcp(it[12])?|rsqit1|rsqrt|subr?))\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.3dnow.simd
    - match: '(?i)\b(pfcmp(eq|ge|gt))\b'
      scope: keyword.operator.word.mnemonic.supplemental.amd.3dnow.comparison
  mnemonics-supplemental-cyrix:
    - match: '(?i)\b((sv|rs)dc|(wr|rd)shr|paddsiw)\b'
      scope: keyword.operator.word.mnemonic.supplemental.cyrix
  mnemonics-supplemental-via:
    - match: '(?i)\b(montmul)\b'
      scope: keyword.operator.word.mnemonic.supplemental.via
    - match: '(?i)\b(x(store(rng)?|crypt(ecb|cbc|ctr|cfb|ofb)|sha(1|256)))\b'
      scope: keyword.operator.word.mnemonic.supplemental.via.padlock
  mnemonics-undocumented:
    - match: '(?i)\b(ret[nf]|icebp|int1|int03|smi|ud1)\b'
      scope: keyword.operator.word.mnemonic.undocumented
  mnemonics-future-intel:
    - include: mnemonics-future-intel-avx512
    - include: mnemonics-future-intel-opmask
    - include: mnemonics-future-intel-cet
    - include: mnemonics-future-intel-other
  mnemonics-future-intel-avx512:
    - match: '(?i)\b(vblendm(pd|ps)|vpblendm[bdqw])\b'
      scope: keyword.operator.word.mnemonic.avx512.blend
    - match: '(?i)\b(vbroadcast[fi](32x[248]|64x[24])|v(extract|insert)[fi](32x[48]|64x[24])|vshuf[fi](32x4|64x2)|vpbroadcastm(b2q|w2d))\b'
      scope: keyword.operator.word.mnemonic.avx512.bits-mov
    - match: '(?i)\b(v(compress|expand)p[ds]|vp(compress|expand|conflict)[dq])\b'
      scope: keyword.operator.word.mnemonic.avx512.compress
    - match: '(?i)\b(vcvt(t?p[sd]2(udq|u?qq)|(udq|u?qq)2p[ds]|t?s[ds]2usi|usi2s[ds]))\b'
      scope: keyword.operator.word.mnemonic.avx512.conversion
    - match: '(?i)\b(v(fixupimm|fpclass|get(exp|mant)|range|(rcp|rsqrt)(14|28)|reduce|rndscale|scalef)([ps][ds]))\b'
      scope: keyword.operator.word.mnemonic.avx512.math
    - match: '(?i)\b(v(exp2p[ds]|(scatter|(gather|scatter)pf[01])[dq]p[ds]))\b'
      scope: keyword.operator.word.mnemonic.avx512.math
    - match: '(?i)\b(vmovdq(a(32|64)|u(8|16|32|64)))\b'
      scope: keyword.operator.word.mnemonic.avx512.simd-integer
    - match: '(?i)\b(vp(andn?|x?or)[dq])\b'
      scope: keyword.operator.word.mnemonic.avx512.logical
    - match: '(?i)\b(vpcmpu?[dqw])\b'
      scope: keyword.operator.word.mnemonic.avx512.packed-comparison
    - match: '(?i)\b(vp(absq|(lzcnt|ternlog)[dq]|madd52[lh]uq|(max|min)[su]q|mullq))\b'
      scope: keyword.operator.word.mnemonic.avx512.packed-math
    - match: '(?i)\b(vpmov(m2[bdqw]|[bdqw]2m|(u?s)?([qd][bw]|qd|wb)))\b'
      scope: keyword.operator.word.mnemonic.avx512.packed-mov
    - match: '(?i)\b(vp(ro[rl]v?[dq]|scatter[dq][dq]))\b'
      scope: keyword.operator.word.mnemonic.avx512.packed-shift
    - match: '(?i)\b(vptestn?m[bdqw])\b'
      scope: keyword.operator.word.mnemonic.avx512.packed-test
    - match: '(?i)\b(vperm([bdw]|[it]2([bdwq]|p[ds])))\b'
      scope: keyword.operator.word.mnemonic.avx512.permutations
    - match: '(?i)\b(valign[dq]|vdbpsadbw|vpmultishiftqb|vpsrav[dqw])\b'
      scope: keyword.operator.word.mnemonic.avx512.other
  mnemonics-future-intel-opmask:
    - match: '(?i)\b(k(add|andn?|(xn?)?or|mov|not|(or)?test|shift[lr])[bdqw]|kunpck(bw|wd|dq))\b'
      scope: keyword.operator.word.mnemonic.opmask
  mnemonics-future-intel-cet:
    - match: '(?i)\b((inc|save|rstor)ssp|wru?ss|(set|clr)ssbsy|endbranch|endbr(32|64))\b'
      scope: keyword.operator.word.mnemonic.cet
  mnemonics-future-intel-other:
    - match: '(?i)\b(clflushopt|clwb|pcommit)\b'
      scope: keyword.operator.word.mnemonic.other
  mnemonics-pseudo-ops:
    - match: '(?i)\b(cmp(n?(eq|lt|le)|(un)?ord)[ps][ds])\b'
      scope: keyword.pseudo-mnemonic.sse2.compare
    - match: '(?i)\b(v?pclmul([hl]q[hl]q|[hl]qh)dq)\b'
      scope: keyword.pseudo-mnemonic.avx.promoted.aes
    - match: '(?i)\b(vcmp(eq(_(os|uq|us))?|neq(_(oq|os|us))?|[gl][et](_oq)?|n[gl][et](_uq)?|(un)?ord(_s)?|false(_os)?|true(_us)?)[ps][ds])\b'
      scope: keyword.pseudo-mnemonic.avx.promoted.comparison
    - match: '(?i)\b(vpcom(n?eq|[gl][et]|false|true)(b|uw))\b'
      scope: keyword.pseudo-mnemonic.supplemental.amd.xop.simd
  mnemonics-invalid:
    - include: mnemonics-invalid-amd-sse5
  mnemonics-invalid-amd-sse5:
    - match: '(?i)\b(com[ps][ds]|pcomu?[bdqw])\b'
      scope: invalid.keyword.operator.word.mnemonic.sse5.comparison
    - match: '(?i)\b(cvtp(h2ps|s2ph)|frcz[ps][ds])\b'
      scope: invalid.keyword.operator.word.mnemonic.sse5.conversion
    - match: '(?i)\b(fn?m((add|sub)[ps][ds])|ph(addu?(b[dqw]|w[dq]|dq)|sub(bw|dq|wd))|pma(css?(d(d|q[hl])|w[dw])|dcss?wd))\b'
      scope: invalid.keyword.operator.word.mnemonic.sse5.packed-arithmetic
    - match: '(?i)\b(pcmov|permp[ds]|pperm|prot[bdqw]|psh[al][bdqw])\b'
      scope: invalid.keyword.operator.word.mnemonic.sse5.simd-integer
...