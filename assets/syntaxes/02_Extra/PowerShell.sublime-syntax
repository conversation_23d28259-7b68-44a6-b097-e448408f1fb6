%YAML 1.2
---
# http://www.sublimetext.com/docs/3/syntax.html
name: PowerShell
file_extensions:
  - ps1
  - psm1
  - psd1
scope: source.powershell
contexts:
  main:
    - match: "<#"
      captures:
        0: punctuation.definition.comment.block.begin.powershell
      push:
        - meta_scope: comment.block.powershell
        - match: "#>"
          captures:
            0: punctuation.definition.comment.block.end.powershell
          pop: true
        - include: commentEmbeddedDocs
    - match: '[2-6]>&1|>>|>|<<|<|>|>\||[1-6]>|[1-6]>>'
      scope: keyword.operator.redirection.powershell
    - include: commands
    - include: commentLine
    - include: variable
    - include: interpolatedStringContent
    - include: function
    - include: attribute
    - include: UsingDirective
    - include: type
    - include: hashtable
    - include: doubleQuotedString
    - include: scriptblock
    - include: doubleQuotedStringEscapes
    - match: (?<!')'
      captures:
        0: punctuation.definition.string.begin.powershell
      push:
        - meta_scope: string.quoted.single.powershell
        - match: "'(?!')"
          captures:
            0: punctuation.definition.string.end.powershell
          pop: true
        - match: "''"
          scope: constant.character.escape.powershell
    - match: \@"(?=$)
      push:
        - meta_scope: string.quoted.double.heredoc.powershell
        - match: ^"@
          pop: true
        - include: variableNoProperty
        - include: doubleQuotedStringEscapes
        - include: interpolation
    - match: \@'(?=$)
      push:
        - meta_scope: string.quoted.single.heredoc.powershell
        - match: ^'@
          pop: true
        - match: "''"
          scope: constant.character.escape.powershell
    - include: numericConstant
    - match: (@)(\()
      captures:
        1: keyword.other.array.begin.powershell
        2: punctuation.section.group.begin.powershell
      push:
        - meta_scope: meta.group.array-expression.powershell
        - match: \)
          captures:
            0: punctuation.section.group.end.powershell
          pop: true
        - include: main
    - match: (\$)(\()
      comment: "TODO: move to repo; make recursive."
      captures:
        1: punctuation.definition.variable.powershell
        2: punctuation.section.group.begin.powershell
      push:
        - meta_scope: meta.group.complex.subexpression.powershell
        - match: \)
          captures:
            0: punctuation.section.group.end.powershell
          pop: true
        - include: main
    - match: '(\b(([A-Za-z0-9\-_\.]+)\.(?i:exe|com|cmd|bat))\b)'
      scope: support.function.powershell
    - match: (?<!\w|-|\.)((?i:begin|break|catch|continue|data|default|define|do|dynamicparam|else|elseif|end|exit|finally|for|from|if|in|inlinescript|parallel|param|process|return|sequence|switch|throw|trap|try|until|var|while)|%|\?)(?!\w)
      scope: keyword.control.powershell
    - match: '(?<!\w|-|[^\)]\.)((?i:(foreach|where)(?!-object))|%|\?)(?!\w)'
      scope: keyword.control.powershell
    - match: (?<!\w)(--%)(?!\w)
      comment: This should be moved to the repository at some point.
      captures:
        1: keyword.control.powershell
      push:
        - match: $
          pop: true
        - match: .+
          scope: string.unquoted.powershell
    - match: (?<!\w)((?i:hidden|static))(?!\w)
      comment: This should only be relevant inside a class but will require a rework of how classes are matched. This is a temp fix.
      scope: storage.modifier.powershell
    - match: '(?<!\w|-)((?i:class)|%|\?)(?:\s)+((?:\p{L}|\d|_|-|)+)\b'
      comment: capture should be entity.name.type, but it doesn't provide a good color in the default schema.
      captures:
        1: storage.type.powershell
        2: entity.name.function
    - match: (?<!\w)-(?i:is(?:not)?|as)\b
      scope: keyword.operator.comparison.powershell
    - match: '(?<!\w)-(?i:[ic]?(?:eq|ne|[gl][te]|(?:not)?(?:like|match|contains|in)|replace))(?!\p{L})'
      scope: keyword.operator.comparison.powershell
    - match: '(?<!\w)-(?i:join|split)(?!\p{L})|!'
      scope: keyword.operator.unary.powershell
    - match: '(?<!\w)-(?i:and|or|not|xor)(?!\p{L})|!'
      scope: keyword.operator.logical.powershell
    - match: '(?<!\w)-(?i:band|bor|bnot|bxor|shl|shr)(?!\p{L})'
      scope: keyword.operator.bitwise.powershell
    - match: '(?<!\w)-(?i:f)(?!\p{L})'
      scope: keyword.operator.string-format.powershell
    - match: "[+%*/-]?=|[+/*%-]"
      scope: keyword.operator.assignment.powershell
    - match: '\|{2}|&{2}|;'
      scope: punctuation.terminator.statement.powershell
    - match: '&|(?<!\w)\.(?= )|`|,|\|'
      scope: keyword.operator.other.powershell
    - match: (?<!\s|^)\.\.(?=\-?\d|\(|\$)
      comment: This is very imprecise, is there a syntax for 'must come after...'
      scope: keyword.operator.range.powershell
  RequiresDirective:
    - match: (?<=#)(?i:(requires))\s
      captures:
        0: keyword.control.requires.powershell
      push:
        - meta_scope: meta.requires.powershell
        - match: $
          pop: true
        - match: \-(?i:Modules|PSSnapin|RunAsAdministrator|ShellId|Version)
          scope: keyword.other.powershell
        - match: '(?<!-)\b\p{L}+|\d+(?:\.\d+)*'
          scope: variable.parameter.powershell
        - include: hashtable
  UsingDirective:
    - match: (?<!\w)(?i:(using))\s+(?i:(namespace|module))\s+(?i:((?:\w+(?:\.)?)+))
      captures:
        1: keyword.control.using.powershell
        2: keyword.other.powershell
        3: variable.parameter.powershell
  attribute:
    - match: '(\[)\s*\b(?i)(cmdletbinding|alias|outputtype|parameter|validatenotnull|validatenotnullorempty|validatecount|validateset|allownull|allowemptycollection|allowemptystring|validatescript|validaterange|validatepattern|validatelength|supportswildcards)\b'
      captures:
        1: punctuation.section.bracket.begin.powershell
        2: support.function.attribute.powershell
      push:
        - meta_scope: meta.attribute.powershell
        - match: '(\])'
          captures:
            1: punctuation.section.bracket.end.powershell
          pop: true
        - match: \(
          captures:
            0: punctuation.section.group.begin.powershell
          push:
            - match: \)
              captures:
                0: punctuation.section.group.end.powershell
              pop: true
            - include: main
            - match: (?i)\b(mandatory|valuefrompipeline|valuefrompipelinebypropertyname|valuefromremainingarguments|position|parametersetname|defaultparametersetname|supportsshouldprocess|supportspaging|positionalbinding|helpuri|confirmimpact|helpmessage)\b(?:\s+)?(=)?
              captures:
                1: variable.parameter.attribute.powershell
                2: keyword.operator.assignment.powershell
  commands:
    - match: '(?:(\p{L}|\d|_|-|\\|\:)*\\)?\b(?i:Add|Approve|Assert|Backup|Block|Build|Checkpoint|Clear|Close|Compare|Complete|Compress|Confirm|Connect|Convert|ConvertFrom|ConvertTo|Copy|Debug|Deny|Deploy|Disable|Disconnect|Dismount|Edit|Enable|Enter|Exit|Expand|Export|Find|Format|Get|Grant|Group|Hide|Import|Initialize|Install|Invoke|Join|Limit|Lock|Measure|Merge|Mount|Move|New|Open|Optimize|Out|Ping|Pop|Protect|Publish|Push|Read|Receive|Redo|Register|Remove|Rename|Repair|Request|Reset|Resize|Resolve|Restart|Restore|Resume|Revoke|Save|Search|Select|Send|Set|Show|Skip|Split|Start|Step|Stop|Submit|Suspend|Switch|Sync|Test|Trace|Unblock|Undo|Uninstall|Unlock|Unprotect|Unpublish|Unregister|Update|Use|Wait|Watch|Write)\-.+?(?:\.(?i:exe|cmd|bat|ps1))?\b'
      comment: "Verb-Noun pattern:"
      scope: support.function.powershell
    - match: (?<!\w)(?i:foreach-object)(?!\w)
      comment: Builtin cmdlets with reserved verbs
      scope: support.function.powershell
    - match: (?<!\w)(?i:where-object)(?!\w)
      comment: Builtin cmdlets with reserved verbs
      scope: support.function.powershell
    - match: (?<!\w)(?i:sort-object)(?!\w)
      comment: Builtin cmdlets with reserved verbs
      scope: support.function.powershell
    - match: (?<!\w)(?i:tee-object)(?!\w)
      comment: Builtin cmdlets with reserved verbs
      scope: support.function.powershell
  commentEmbeddedDocs:
    - match: ^(?i:(?:\s?|#)+(\.)(COMPONENT|DESCRIPTION|EXAMPLE|EXTERNALHELP|FORWARDHELPCATEGORY|FORWARDHELPTARGETNAME|FUNCTIONALITY|INPUTS|LINK|NOTES|OUTPUTS|REMOTEHELPRUNSPACE|ROLE|SYNOPSIS))
      scope: comment.documentation.embedded.powershell
      captures:
        1: constant.string.documentation.powershell
        2: keyword.operator.documentation.powershell
    - match: '(?i:\s?(\.)(PARAMETER|FORWARDHELPTARGETNAME|FORWARDHELPCATEGORY|REMOTEHELPRUNSPACE|EXTERNALHELP)\s+([a-z0-9-_]+))'
      scope: comment.documentation.embedded.powershell
      captures:
        1: constant.string.documentation.powershell
        2: keyword.operator.documentation.powershell
        3: keyword.operator.documentation.powershell
  commentLine:
    - match: '(?<![`\\-])#'
      captures:
        0: punctuation.definition.comment.powershell
      push:
        - meta_scope: comment.line.powershell
        - match: $\n?
          captures:
            0: punctuation.definition.comment.powershell
          pop: true
        - include: commentEmbeddedDocs
        - include: RequiresDirective
  doubleQuotedString:
    - match: (?<!(?<!`)")"
      captures:
        0: punctuation.definition.string.begin.powershell
      push:
        - meta_scope: string.quoted.double.powershell
        - match: '"(?!")'
          captures:
            0: punctuation.definition.string.end.powershell
          pop: true
        - match: '(?i)\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,64}\b'
        - include: variableNoProperty
        - include: doubleQuotedStringEscapes
        - include: interpolation
        - match: '`\s*$'
          scope: keyword.other.powershell
  doubleQuotedStringEscapes:
    - match: '`[`0abefnrtv"''$]'
      scope: constant.character.escape.powershell
    - include: unicodeEscape
    - match: '""'
      scope: constant.character.escape.powershell
  function:
    - match: '^(?:\s*+)(?i)(function|filter|configuration|workflow)\s+(?:(global|local|script|private):)?((?:\p{L}|\d|_|-|\.)+)'
      captures:
        0: meta.function.powershell
        1: storage.type.powershell
        2: storage.modifier.scope.powershell
        3: entity.name.function.powershell
      push:
        - match: '(?=\{|\()'
          pop: true
        - include: commentLine
  hashtable:
    - match: '(@)(\{)'
      captures:
        1: keyword.other.hashtable.begin.powershell
        2: punctuation.section.braces.begin.powershell
      push:
        - meta_scope: meta.hashtable.powershell
        - match: '(\})'
          captures:
            1: punctuation.section.braces.end.powershell
          pop: true
        - match: \b((?:\'|\")?)(\w+)((?:\'|\")?)(?:\s+)?(=)(?:\s+)?
          scope: meta.hashtable.assignment.powershell
          captures:
            1: punctuation.definition.string.begin.powershell
            2: variable.other.readwrite.powershell
            3: punctuation.definition.string.end.powershell
            4: keyword.operator.assignment.powershell
        - include: scriptblock
        - include: main
  interpolatedStringContent:
    - match: \(
      captures:
        0: punctuation.section.group.begin.powershell
      push:
        - meta_content_scope: interpolated.simple.source.powershell
        - match: \)
          captures:
            0: punctuation.section.group.end.powershell
          pop: true
        - include: main
        - include: interpolation
        - include: interpolatedStringContent
  interpolation:
    - match: (\$)(\()
      captures:
        1: punctuation.definition.variable.powershell
        2: punctuation.section.group.begin.powershell
      push:
        - meta_content_scope: interpolated.complex.source.powershell
        - match: \)
          captures:
            0: punctuation.section.group.end.powershell
          pop: true
        - include: main
        - include: interpolation
        - include: interpolatedStringContent
  numericConstant:
    - match: '(?<!\w)([-+]?0(?:x|X)[0-9a-fA-F_]+(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.hex.powershell
        2: keyword.other.powershell
    - match: '(?<!\w)([-+]?(?:[0-9_]+)?\.[0-9_]+(?:(?:e|E)[0-9]+)?(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.integer.powershell
        2: keyword.other.powershell
    - match: '(?<!\w)([-+]?0(?:b|B)[01_]+(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.octal.powershell
        2: keyword.other.powershell
    - match: '(?<!\w)([-+]?[0-9_]+(?:e|E)(?:[0-9_])?+(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.integer.powershell
        2: keyword.other.powershell
    - match: '(?<!\w)([-+]?[0-9_]+\.(?:e|E)(?:[0-9_])?+(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.integer.powershell
        2: keyword.other.powershell
    - match: '(?<!\w)([-+]?[0-9_]+[\.]?(?:F|f|D|d|M|m))((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.integer.powershell
        2: keyword.other.powershell
    - match: '(?<!\w)([-+]?[0-9_]+[\.]?(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\b'
      captures:
        1: constant.numeric.integer.powershell
        2: keyword.other.powershell
  scriptblock:
    - match: '\{'
      captures:
        0: punctuation.section.braces.begin.powershell
      push:
        - meta_scope: meta.scriptblock.powershell
        - match: '\}'
          captures:
            0: punctuation.section.braces.end.powershell
          pop: true
        - include: main
  type:
    - match: '\['
      captures:
        0: punctuation.section.bracket.begin.powershell
      push:
        - match: '\]'
          captures:
            0: punctuation.section.bracket.end.powershell
          pop: true
        - match: '(?!\d+|\.)(?:\p{L}|\p{N}|\.)+'
          scope: storage.type.powershell
        - include: main
  unicodeEscape:
    - match: '`u\{(?:(?:10)?([0-9a-fA-F]){1,4}|0?\g<1>{1,5})}'
      scope: constant.character.escape.powershell
    - match: '`u(?:\{[0-9a-fA-F]{,6}.)?'
      scope: invalid.character.escape.powershell
  variable:
    - match: (\$)(?i:(False|Null|True))\b
      comment: These are special constants.
      captures:
        0: constant.language.powershell
        1: punctuation.definition.variable.powershell
    - match: '(\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))((?:\.(?:\p{L}|\d|_)+)*\b)?\b'
      comment: These are the other built-in constants.
      captures:
        0: support.constant.variable.powershell
        1: punctuation.definition.variable.powershell
        3: variable.other.member.powershell
    - match: '(\$)((?:[$^?])|(?i:_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This)\b)((?:\.(?:\p{L}|\d|_)+)*\b)?'
      comment: Automatic variables are not constants, but they are read-only. In monokai (default) color schema support.variable doesn't have color, so we use constant.
      captures:
        0: support.variable.automatic.powershell
        1: punctuation.definition.variable.powershell
        3: variable.other.member.powershell
    - match: '(\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|InformationPreference|LogCommandHealthEvent|LogCommandLifecycleEvent|LogEngineHealthEvent|LogEngineLifecycleEvent|LogProviderHealthEvent|LogProviderLifecycleEvent|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|PSCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoLoadingPreference|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|ProgressPreference|VerbosePreference|WarningPreference|WhatIfPreference))((?:\.(?:\p{L}|\d|_)+)*\b)?\b'
      comment: Style preference variables as language variables so that they stand out.
      captures:
        0: variable.language.powershell
        1: punctuation.definition.variable.powershell
        3: variable.other.member.powershell
    - match: '(?i:(\$|@)(global|local|private|script|using|workflow):((?:\p{L}|\d|_)+))((?:\.(?:\p{L}|\d|_)+)*\b)?'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: storage.modifier.scope.powershell
        4: variable.other.member.powershell
    - match: '(?i:(\$)(\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\}))((?:\.(?:\p{L}|\d|_)+)*\b)?'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: punctuation.section.braces.begin.powershell
        3: storage.modifier.scope.powershell
        5: punctuation.section.braces.end.powershell
        6: variable.other.member.powershell
    - match: '(?i:(\$|@)((?:\p{L}|\d|_)+:)?((?:\p{L}|\d|_)+))((?:\.(?:\p{L}|\d|_)+)*\b)?'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: support.variable.drive.powershell
        4: variable.other.member.powershell
    - match: '(?i:(\$)(\{)((?:\p{L}|\d|_)+:)?([^}]*[^}`])(\}))((?:\.(?:\p{L}|\d|_)+)*\b)?'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: punctuation.section.braces.begin.powershell
        3: support.variable.drive.powershell
        5: punctuation.section.braces.end.powershell
        6: variable.other.member.powershell
  variableNoProperty:
    - match: (\$)(?i:(False|Null|True))\b
      comment: These are special constants.
      captures:
        0: constant.language.powershell
        1: punctuation.definition.variable.powershell
    - match: (\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))\b
      comment: These are the other built-in constants.
      captures:
        0: support.constant.variable.powershell
        1: punctuation.definition.variable.powershell
        3: variable.other.member.powershell
    - match: '(\$)((?:[$^?])|(?i:_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This)\b)'
      comment: Automatic variables are not constants, but they are read-only...
      captures:
        0: support.variable.automatic.powershell
        1: punctuation.definition.variable.powershell
        3: variable.other.member.powershell
    - match: (\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|InformationPreference|LogCommandHealthEvent|LogCommandLifecycleEvent|LogEngineHealthEvent|LogEngineLifecycleEvent|LogProviderHealthEvent|LogProviderLifecycleEvent|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|PSCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoLoadingPreference|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|ProgressPreference|VerbosePreference|WarningPreference|WhatIfPreference))\b
      comment: Style preference variables as language variables so that they stand out.
      captures:
        0: variable.language.powershell
        1: punctuation.definition.variable.powershell
        3: variable.other.member.powershell
    - match: '(?i:(\$)(global|local|private|script|using|workflow):((?:\p{L}|\d|_)+))'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: storage.modifier.scope.powershell
        4: variable.other.member.powershell
    - match: '(?i:(\$)(\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\}))'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: storage.modifier.scope.powershell
        4: keyword.other.powershell
        5: variable.other.member.powershell
    - match: '(?i:(\$)((?:\p{L}|\d|_)+:)?((?:\p{L}|\d|_)+))'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: support.variable.drive.powershell
        4: variable.other.member.powershell
    - match: '(?i:(\$)(\{)((?:\p{L}|\d|_)+:)?([^}]*[^}`])(\}))'
      captures:
        0: variable.other.readwrite.powershell
        1: punctuation.definition.variable.powershell
        2: punctuation.section.braces.begin
        3: support.variable.drive.powershell
        5: punctuation.section.braces.end
