%YAML 1.2
---
# http://www.sublimetext.com/docs/3/syntax.html
name: TypeScriptReact
file_extensions:
  - tsx
scope: source.tsx
contexts:
  main:
    - include: directives
    - include: statements
    - match: \A(#!).*(?=$)
      scope: comment.line.shebang.ts
      captures:
        1: punctuation.definition.comment.ts
  comment:
    - match: /\*\*(?!/)
      captures:
        0: punctuation.definition.comment.tsx
      push:
        - meta_scope: comment.block.documentation.tsx
        - match: \*/
          captures:
            0: punctuation.definition.comment.tsx
          pop: true
        - include: docblock
    - match: (/\*)(?:\s*((@)internal)(?=\s|(\*/)))?
      captures:
        1: punctuation.definition.comment.tsx
        2: storage.type.internaldeclaration.tsx
        3: punctuation.decorator.internaldeclaration.tsx
      push:
        - meta_scope: comment.block.tsx
        - match: \*/
          captures:
            0: punctuation.definition.comment.tsx
          pop: true
    - match: '(^[ \t]+)?((//)(?:\s*((@)internal)(?=\s|$))?)'
      captures:
        1: punctuation.whitespace.comment.leading.tsx
        2: comment.line.double-slash.tsx
        3: punctuation.definition.comment.tsx
        4: storage.type.internaldeclaration.tsx
        5: punctuation.decorator.internaldeclaration.tsx
      push:
        - meta_content_scope: comment.line.double-slash.tsx
        - match: (?=^)
          pop: true
  access-modifier:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(abstract|public|protected|private|readonly|static)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: storage.modifier.tsx
  after-operator-block-as-object-literal:
    - match: '(?<!\+\+|--)(?<=[:=(,\[?+!>]|^await|[^\._$[:alnum:]]await|^return|[^\._$[:alnum:]]return|^yield|[^\._$[:alnum:]]yield|^throw|[^\._$[:alnum:]]throw|^in|[^\._$[:alnum:]]in|^of|[^\._$[:alnum:]]of|^typeof|[^\._$[:alnum:]]typeof|&&|\|\||\*)\s*(\{)'
      captures:
        1: punctuation.definition.block.tsx
      push:
        - meta_scope: meta.objectliteral.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - include: object-member
  array-binding-pattern:
    - match: '(?:(\.\.\.)\s*)?(\[)'
      captures:
        1: keyword.operator.rest.tsx
        2: punctuation.definition.binding-pattern.array.tsx
      push:
        - match: '\]'
          captures:
            0: punctuation.definition.binding-pattern.array.tsx
          pop: true
        - include: binding-element
        - include: punctuation-comma
  array-binding-pattern-const:
    - match: '(?:(\.\.\.)\s*)?(\[)'
      captures:
        1: keyword.operator.rest.tsx
        2: punctuation.definition.binding-pattern.array.tsx
      push:
        - match: '\]'
          captures:
            0: punctuation.definition.binding-pattern.array.tsx
          pop: true
        - include: binding-element-const
        - include: punctuation-comma
  array-literal:
    - match: '\s*(\[)'
      captures:
        1: meta.brace.square.tsx
      push:
        - meta_scope: meta.array.literal.tsx
        - match: '\]'
          captures:
            0: meta.brace.square.tsx
          pop: true
        - include: expression
        - include: punctuation-comma
  arrow-function:
    - match: '(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(\basync)\s+)?([_$[:alpha:]][_$[:alnum:]]*)\s*(?==>)'
      scope: meta.arrow.tsx
      captures:
        1: storage.modifier.async.tsx
        2: variable.parameter.tsx
    - match: |-
        (?x) (?:
          (?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(\basync)
        )? ((?<![})!\]])\s*
          (?=
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          )
        )
      captures:
        1: storage.modifier.async.tsx
      push:
        - meta_scope: meta.arrow.tsx
        - match: '(?==>|\{|(^\s*(export|function|class|interface|let|var|const|import|enum|namespace|module|type|abstract|declare)\s+))'
          pop: true
        - include: comment
        - include: type-parameters
        - include: function-parameters
        - include: arrow-return-type
    - match: "=>"
      captures:
        0: storage.type.function.arrow.tsx
      push:
        - meta_scope: meta.arrow.tsx
        - match: '(?<=\}|\S)(?<!=>)|((?!\{)(?=\S))'
          pop: true
        - include: decl-block
        - include: expression
  arrow-return-type:
    - match: (?<=\))\s*(:)
      captures:
        1: keyword.operator.type.annotation.tsx
      push:
        - meta_scope: meta.return.type.arrow.tsx
        - match: '(?==>|\{|(^\s*(export|function|class|interface|let|var|const|import|enum|namespace|module|type|abstract|declare)\s+))'
          pop: true
        - include: arrow-return-type-body
  arrow-return-type-body:
    - match: '(?<=[:])(?=\s*\{)'
      push:
        - match: '(?<=\})'
          pop: true
        - include: type-object
    - include: type-predicate-operator
    - include: type
  async-modifier:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(async)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: storage.modifier.async.tsx
  binding-element:
    - include: comment
    - include: string
    - include: object-binding-pattern
    - include: array-binding-pattern
    - include: destructuring-variable-rest
    - include: variable-initializer
  binding-element-const:
    - include: comment
    - include: string
    - include: object-binding-pattern-const
    - include: array-binding-pattern-const
    - include: destructuring-variable-rest-const
    - include: variable-initializer
  boolean-literal:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))true(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: constant.language.boolean.true.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))false(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: constant.language.boolean.false.tsx
  brackets:
    - match: "{"
      push:
        - match: '}|(?=\*/)'
          pop: true
        - include: brackets
    - match: '\['
      push:
        - match: '\]|(?=\*/)'
          pop: true
        - include: brackets
  cast:
    - include: jsx
  class-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(export)\s+)?\b(?:(abstract)\s+)?\b(class)\b(?=\s+|/[/*])'
      captures:
        1: keyword.control.export.tsx
        2: storage.modifier.tsx
        3: storage.type.class.tsx
      push:
        - meta_scope: meta.class.tsx
        - match: '(?<=\})'
          pop: true
        - include: class-declaration-or-expression-patterns
  class-declaration-or-expression-patterns:
    - include: comment
    - include: class-or-interface-heritage
    - match: "[_$[:alpha:]][_$[:alnum:]]*"
      captures:
        0: entity.name.type.class.tsx
    - include: type-parameters
    - include: class-or-interface-body
  class-expression:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(class)\b(?=\s+|[<{]|\/[\/*])'
      captures:
        1: storage.type.class.tsx
      push:
        - meta_scope: meta.class.tsx
        - match: '(?<=\})'
          pop: true
        - include: class-declaration-or-expression-patterns
  class-or-interface-body:
    - match: '\{'
      captures:
        0: punctuation.definition.block.tsx
      push:
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - include: comment
        - include: decorator
        - include: method-declaration
        - include: indexer-declaration
        - include: field-declaration
        - include: string
        - include: type-annotation
        - include: variable-initializer
        - include: access-modifier
        - include: property-accessor
        - include: async-modifier
        - include: after-operator-block-as-object-literal
        - include: decl-block
        - include: expression
        - include: punctuation-comma
        - include: punctuation-semicolon
  class-or-interface-heritage:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:\b(extends|implements)\b)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: storage.modifier.tsx
      push:
        - match: '(?=\{)'
          pop: true
        - include: comment
        - include: class-or-interface-heritage
        - include: type-parameters
        - include: expressionWithoutIdentifiers
        - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))(?=\s*[_$[:alpha:]][_$[:alnum:]]*(\s*\??\.\s*[_$[:alpha:]][_$[:alnum:]]*)*\s*)'
          captures:
            1: entity.name.type.module.tsx
            2: punctuation.accessor.tsx
            3: punctuation.accessor.optional.tsx
        - match: "([_$[:alpha:]][_$[:alnum:]]*)"
          captures:
            1: entity.other.inherited-class.tsx
        - include: expressionPunctuations
  control-statement:
    - include: switch-statement
    - include: for-loop
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(catch|finally|throw|try)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.trycatch.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(break|continue|goto)\s+([_$[:alpha:]][_$[:alnum:]]*)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: keyword.control.loop.tsx
        2: entity.name.label.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(break|continue|do|goto|while)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.loop.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(return)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.flow.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(case|default|switch)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.switch.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(else|if)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.conditional.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(with)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.with.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(package)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(debugger)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.other.debugger.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(declare)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: storage.modifier.tsx
  decl-block:
    - match: '\{'
      captures:
        0: punctuation.definition.block.tsx
      push:
        - meta_scope: meta.block.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - include: statements
  declaration:
    - include: decorator
    - include: var-expr
    - include: function-declaration
    - include: class-declaration
    - include: interface-declaration
    - include: enum-declaration
    - include: namespace-declaration
    - include: type-alias-declaration
    - include: import-equals-declaration
    - include: import-declaration
    - include: export-declaration
  decorator:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))\@'
      captures:
        0: punctuation.decorator.tsx
      push:
        - meta_scope: meta.decorator.tsx
        - match: (?=\s)
          pop: true
        - include: expression
  destructuring-const:
    - match: '(?<!=|:|^of|[^\._$[:alnum:]]of|^in|[^\._$[:alnum:]]in)\s*(?=\{)'
      push:
        - meta_scope: meta.object-binding-pattern-variable.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: object-binding-pattern-const
        - include: type-annotation
        - include: comment
    - match: '(?<!=|:|^of|[^\._$[:alnum:]]of|^in|[^\._$[:alnum:]]in)\s*(?=\[)'
      push:
        - meta_scope: meta.array-binding-pattern-variable.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: array-binding-pattern-const
        - include: type-annotation
        - include: comment
  destructuring-parameter:
    - match: '(?<!=|:)\s*(\{)'
      captures:
        1: punctuation.definition.binding-pattern.object.tsx
      push:
        - meta_scope: meta.parameter.object-binding-pattern.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.binding-pattern.object.tsx
          pop: true
        - include: parameter-object-binding-element
    - match: '(?<!=|:)\s*(\[)'
      captures:
        1: punctuation.definition.binding-pattern.array.tsx
      push:
        - meta_scope: meta.paramter.array-binding-pattern.tsx
        - match: '\]'
          captures:
            0: punctuation.definition.binding-pattern.array.tsx
          pop: true
        - include: parameter-binding-element
        - include: punctuation-comma
  destructuring-parameter-rest:
    - match: '(?:(\.\.\.)\s*)?([_$[:alpha:]][_$[:alnum:]]*)'
      captures:
        1: keyword.operator.rest.tsx
        2: variable.parameter.tsx
  destructuring-variable:
    - match: '(?<!=|:|^of|[^\._$[:alnum:]]of|^in|[^\._$[:alnum:]]in)\s*(?=\{)'
      push:
        - meta_scope: meta.object-binding-pattern-variable.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: object-binding-pattern
        - include: type-annotation
        - include: comment
    - match: '(?<!=|:|^of|[^\._$[:alnum:]]of|^in|[^\._$[:alnum:]]in)\s*(?=\[)'
      push:
        - meta_scope: meta.array-binding-pattern-variable.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: array-binding-pattern
        - include: type-annotation
        - include: comment
  destructuring-variable-rest:
    - match: '(?:(\.\.\.)\s*)?([_$[:alpha:]][_$[:alnum:]]*)'
      captures:
        1: keyword.operator.rest.tsx
        2: meta.definition.variable.tsx variable.other.readwrite.tsx
  destructuring-variable-rest-const:
    - match: '(?:(\.\.\.)\s*)?([_$[:alpha:]][_$[:alnum:]]*)'
      captures:
        1: keyword.operator.rest.tsx
        2: meta.definition.variable.tsx variable.other.constant.tsx
  directives:
    - match: '^(///)\s*(?=<(reference|amd-dependency|amd-module)(\s+(path|types|no-default-lib|lib|name)\s*=\s*((\''([^\''\\]|\\\''|\\)*\'')|(\"([^\"\\]|\\\"|\\)*\")))+\s*/>\s*$)'
      captures:
        1: punctuation.definition.comment.tsx
      push:
        - meta_scope: comment.line.triple-slash.directive.tsx
        - match: (?=^)
          pop: true
        - match: (<)(reference|amd-dependency|amd-module)
          captures:
            1: punctuation.definition.tag.directive.tsx
            2: entity.name.tag.directive.tsx
          push:
            - meta_scope: meta.tag.tsx
            - match: />
              captures:
                0: punctuation.definition.tag.directive.tsx
              pop: true
            - match: path|types|no-default-lib|lib|name
              scope: entity.other.attribute-name.directive.tsx
            - match: "="
              scope: keyword.operator.assignment.tsx
            - include: string
  docblock:
    - match: |-
        (?x)
        ((@)(?:access|api))
        \s+
        (private|protected|public)
        \b
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: constant.language.access-type.jsdoc
    - match: |-
        (?x)
        ((@)author)
        \s+
        (
          [^@\s<>*/]
          (?:[^@<>*/]|\*[^/])*
        )
        (?:
          \s*
          (<)
          ([^>\s]+)
          (>)
        )?
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: entity.name.type.instance.jsdoc
        4: punctuation.definition.bracket.angle.begin.jsdoc
        5: constant.other.email.link.underline.jsdoc
        6: punctuation.definition.bracket.angle.end.jsdoc
    - match: |-
        (?x)
        ((@)borrows) \s+
        ((?:[^@\s*/]|\*[^/])+)    # <that namepath>
        \s+ (as) \s+              # as
        ((?:[^@\s*/]|\*[^/])+)    # <this namepath>
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: entity.name.type.instance.jsdoc
        4: keyword.operator.control.jsdoc
        5: entity.name.type.instance.jsdoc
    - match: ((@)example)\s+
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
      push:
        - meta_scope: meta.example.jsdoc
        - match: (?=@|\*/)
          pop: true
        - match: ^\s\*\s+
        - match: \G(<)caption(>)
          captures:
            0: entity.name.tag.inline.jsdoc
            1: punctuation.definition.bracket.angle.begin.jsdoc
            2: punctuation.definition.bracket.angle.end.jsdoc
          push:
            - meta_content_scope: constant.other.description.jsdoc
            - match: (</)caption(>)|(?=\*/)
              captures:
                0: entity.name.tag.inline.jsdoc
                1: punctuation.definition.bracket.angle.begin.jsdoc
                2: punctuation.definition.bracket.angle.end.jsdoc
              pop: true
        - match: '[^\s@*](?:[^*]|\*[^/])*'
          captures:
            0: source.embedded.tsx
    - match: (?x) ((@)kind) \s+ (class|constant|event|external|file|function|member|mixin|module|namespace|typedef) \b
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: constant.language.symbol-type.jsdoc
    - match: |-
        (?x)
        ((@)see)
        \s+
        (?:
          # URL
          (
            (?=https?://)
            (?:[^\s*]|\*[^/])+
          )
          |
          # JSDoc namepath
          (
            (?!
              # Avoid matching bare URIs (also acceptable as links)
              https?://
              |
              # Avoid matching {@inline tags}; we match those below
              (?:\[[^\[\]]*\])? # Possible description [preceding]{@tag}
              {@(?:link|linkcode|linkplain|tutorial)\b
            )
            # Matched namepath
            (?:[^@\s*/]|\*[^/])+
          )
        )
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: variable.other.link.underline.jsdoc
        4: entity.name.type.instance.jsdoc
    - match: |-
        (?x)
        ((@)template)
        \s+
        # One or more valid identifiers
        (
          [A-Za-z_$]         # First character: non-numeric word character
          [\w$.\[\]]*        # Rest of identifier
          (?:                # Possible list of additional identifiers
            \s* , \s*
            [A-Za-z_$]
            [\w$.\[\]]*
          )*
        )
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: variable.other.jsdoc
    - match: |-
        (?x)
        (
          (@)
          (?:arg|argument|const|constant|member|namespace|param|var)
        )
        \s+
        (
          [A-Za-z_$]
          [\w$.\[\]]*
        )
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: variable.other.jsdoc
    - match: '((@)typedef)\s+(?={)'
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
      push:
        - match: '(?=\s|\*/|[^{}\[\]A-Za-z_$])'
          pop: true
        - include: jsdoctype
        - match: '(?:[^@\s*/]|\*[^/])+'
          scope: entity.name.type.instance.jsdoc
    - match: '((@)(?:arg|argument|const|constant|member|namespace|param|prop|property|var))\s+(?={)'
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
      push:
        - match: '(?=\s|\*/|[^{}\[\]A-Za-z_$])'
          pop: true
        - include: jsdoctype
        - match: '([A-Za-z_$][\w$.\[\]]*)'
          scope: variable.other.jsdoc
        - match: |-
            (?x)
            (\[)\s*
            [\w$]+
            (?:
              (?:\[\])?                                        # Foo[ ].bar properties within an array
              \.                                                # Foo.Bar namespaced parameter
              [\w$]+
            )*
            (?:
              \s*
              (=)                                                # [foo=bar] Default parameter value
              \s*
              (
                # The inner regexes are to stop the match early at */ and to not stop at escaped quotes
                (?>
                  "(?:(?:\*(?!/))|(?:\\(?!"))|[^*\\])*?" |                      # [foo="bar"] Double-quoted
                  '(?:(?:\*(?!/))|(?:\\(?!'))|[^*\\])*?' |                      # [foo='bar'] Single-quoted
                  \[ (?:(?:\*(?!/))|[^*])*? \] |                                # [foo=[1,2]] Array literal
                  (?:(?:\*(?!/))|\s(?!\s*\])|\[.*?(?:\]|(?=\*/))|[^*\s\[\]])*   # Everything else
                )*
              )
            )?
            \s*(?:(\])((?:[^*\s]|\*[^\s/])+)?|(?=\*/))
          scope: variable.other.jsdoc
          captures:
            1: punctuation.definition.optional-value.begin.bracket.square.jsdoc
            2: keyword.operator.assignment.jsdoc
            3: source.embedded.tsx
            4: punctuation.definition.optional-value.end.bracket.square.jsdoc
            5: invalid.illegal.syntax.jsdoc
    - match: |-
        (?x)
        (
          (@)
          (?:define|enum|exception|export|extends|lends|implements|modifies
          |namespace|private|protected|returns?|suppress|this|throws|type
          |yields?)
        )
        \s+(?={)
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
      push:
        - match: '(?=\s|\*/|[^{}\[\]A-Za-z_$])'
          pop: true
        - include: jsdoctype
    - match: |-
        (?x)
        (
          (@)
          (?:alias|augments|callback|constructs|emits|event|fires|exports?
          |extends|external|function|func|host|lends|listens|interface|memberof!?
          |method|module|mixes|mixin|name|requires|see|this|typedef|uses)
        )
        \s+
        (
          (?:
            [^{}@\s*] | \*[^/]
          )+
        )
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: entity.name.type.instance.jsdoc
    - match: '((@)(?:default(?:value)?|license|version))\s+(([''''"]))'
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: variable.other.jsdoc
        4: punctuation.definition.string.begin.jsdoc
      push:
        - meta_content_scope: variable.other.jsdoc
        - match: (\3)|(?=$|\*/)
          captures:
            0: variable.other.jsdoc
            1: punctuation.definition.string.end.jsdoc
          pop: true
    - match: '((@)(?:default(?:value)?|license|tutorial|variation|version))\s+([^\s*]+)'
      captures:
        1: storage.type.class.jsdoc
        2: punctuation.definition.block.tag.jsdoc
        3: variable.other.jsdoc
    - match: '(?x) (@) (?:abstract|access|alias|api|arg|argument|async|attribute|augments|author|beta|borrows|bubbles |callback|chainable|class|classdesc|code|config|const|constant|constructor|constructs|copyright |default|defaultvalue|define|deprecated|desc|description|dict|emits|enum|event|example|exception |exports?|extends|extension(?:_?for)?|external|externs|file|fileoverview|final|fires|for|func |function|generator|global|hideconstructor|host|ignore|implements|implicitCast|inherit[Dd]oc |inner|instance|interface|internal|kind|lends|license|listens|main|member|memberof!?|method |mixes|mixins?|modifies|module|name|namespace|noalias|nocollapse|nocompile|nosideeffects |override|overview|package|param|polymer(?:Behavior)?|preserve|private|prop|property|protected |public|read[Oo]nly|record|require[ds]|returns?|see|since|static|struct|submodule|summary |suppress|template|this|throws|todo|tutorial|type|typedef|unrestricted|uses|var|variation |version|virtual|writeOnce|yields?) \b'
      scope: storage.type.class.jsdoc
      captures:
        1: punctuation.definition.block.tag.jsdoc
    - include: inline-tags
  enum-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?(?:\b(const)\s+)?\b(enum)\s+([_$[:alpha:]][_$[:alnum:]]*)'
      captures:
        1: keyword.control.export.tsx
        2: storage.modifier.tsx
        3: storage.type.enum.tsx
        4: entity.name.type.enum.tsx
      push:
        - meta_scope: meta.enum.declaration.tsx
        - match: '(?<=\})'
          pop: true
        - include: comment
        - match: '\{'
          captures:
            0: punctuation.definition.block.tsx
          push:
            - match: '\}'
              captures:
                0: punctuation.definition.block.tsx
              pop: true
            - include: comment
            - match: "([_$[:alpha:]][_$[:alnum:]]*)"
              captures:
                0: variable.other.enummember.tsx
              push:
                - match: '(?=,|\}|$)'
                  pop: true
                - include: comment
                - include: variable-initializer
            - match: '(?=((\''([^\''\\]|\\\''|\\)*\'')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\])))'
              push:
                - match: '(?=,|\}|$)'
                  pop: true
                - include: string
                - include: array-literal
                - include: comment
                - include: variable-initializer
            - include: punctuation-comma
  export-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(export)\s+(as)\s+(namespace)\s+([_$[:alpha:]][_$[:alnum:]]*)'
      captures:
        1: keyword.control.export.tsx
        2: keyword.control.as.tsx
        3: storage.type.namespace.tsx
        4: entity.name.type.module.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(export)(?:(?:\s*(=))|(?:\s+(default)(?=\s+)))'
      captures:
        1: keyword.control.export.tsx
        2: keyword.operator.assignment.tsx
        3: keyword.control.default.tsx
      push:
        - meta_scope: meta.export.default.tsx
        - match: (?=$|;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)
          pop: true
        - include: expression
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(export)(?!\s*:)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        0: keyword.control.export.tsx
      push:
        - meta_scope: meta.export.tsx
        - match: (?=$|;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)
          pop: true
        - include: import-export-declaration
  expression:
    - include: expressionWithoutIdentifiers
    - include: identifiers
    - include: expressionPunctuations
  expression-inside-possibly-arrow-parens:
    - include: expressionWithoutIdentifiers
    - include: function-parameters-body
    - include: identifiers
    - include: expressionPunctuations
  expression-operators:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(await)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.control.flow.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(yield)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))(?=\s*\/\*([^\*]|(\*[^\/]))*\*\/\s*\*)'
      captures:
        1: keyword.control.flow.tsx
      push:
        - match: \*
          captures:
            0: keyword.generator.asterisk.tsx
          pop: true
        - include: comment
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(yield)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))(?:\s*(\*))?'
      captures:
        1: keyword.control.flow.tsx
        2: keyword.generator.asterisk.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))delete(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.expression.delete.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))in(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))(?!\()'
      scope: keyword.operator.expression.in.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))of(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))(?!\()'
      scope: keyword.operator.expression.of.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))instanceof(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.expression.instanceof.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))new(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.new.tsx
    - include: typeof-operator
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))void(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.expression.void.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(as)\s+'
      captures:
        1: keyword.control.as.tsx
      push:
        - match: '(?=$|^|[;,:})\]]|((?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(as)\s+))'
          pop: true
        - include: type
    - match: \.\.\.
      scope: keyword.operator.spread.tsx
    - match: \*=|(?<!\()/=|%=|\+=|\-=
      scope: keyword.operator.assignment.compound.tsx
    - match: \&=|\^=|<<=|>>=|>>>=|\|=
      scope: keyword.operator.assignment.compound.bitwise.tsx
    - match: "<<|>>>|>>"
      scope: keyword.operator.bitwise.shift.tsx
    - match: "===|!==|==|!="
      scope: keyword.operator.comparison.tsx
    - match: <=|>=|<>|<|>
      scope: keyword.operator.relational.tsx
    - match: \!|&&|\|\|
      scope: keyword.operator.logical.tsx
    - match: \&|~|\^|\|
      scope: keyword.operator.bitwise.tsx
    - match: \=
      scope: keyword.operator.assignment.tsx
    - match: "--"
      scope: keyword.operator.decrement.tsx
    - match: \+\+
      scope: keyword.operator.increment.tsx
    - match: '%|\*|/|-|\+'
      scope: keyword.operator.arithmetic.tsx
    - match: '(?<=[_$[:alnum:])\]])\s*(/)(?![/*])'
      captures:
        1: keyword.operator.arithmetic.tsx
  expressionPunctuations:
    - include: punctuation-comma
    - include: punctuation-accessor
  expressionWithoutIdentifiers:
    - include: jsx
    - include: string
    - include: regex
    - include: template
    - include: comment
    - include: function-expression
    - include: class-expression
    - include: arrow-function
    - include: paren-expression-possibly-arrow
    - include: cast
    - include: ternary-expression
    - include: new-expr
    - include: instanceof-expr
    - include: object-literal
    - include: expression-operators
    - include: function-call
    - include: literal
    - include: support-objects
    - include: paren-expression
  field-declaration:
    - match: |-
        (?x)(?<!\()(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(readonly)\s+)?(?=\s*((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\?\s*)?(=|:))
      captures:
        1: storage.modifier.tsx
      push:
        - meta_scope: meta.field.declaration.tsx
        - match: |-
            (?x)(?=\}|;|,|$|(^(?!\s*((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
              (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
              (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
              (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
              (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
              (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
              (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
              (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
              (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
            )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\?\s*)?(=|:))))|(?<=\})
          pop: true
        - include: variable-initializer
        - match: |-
            (?x)(?=((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
              (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
              (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
              (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
              (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
              (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
              (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
              (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
              (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
            )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\?\s*)?(=|:))
          push:
            - match: |-
                (?x)(?=[};,=]|$|(^(?!\s*((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
                  (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
                  (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
                  (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
                  (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
                  (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
                  (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
                  (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
                  (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
                )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\?\s*)?(=|:))))|(?<=\})
              pop: true
            - include: type-annotation
            - include: string
            - include: array-literal
            - include: numeric-literal
            - include: comment
            - match: |-
                (?x)([_$[:alpha:]][_$[:alnum:]]*)(\?)?(?=(\?\s*)?\s*
                # function assignment |
                (=\s*(
                  ((async\s+)?(
                    (function\s*[(<*]) |
                    (function\s+) |
                    ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
                  )) |
                  ((async\s*)?(
                    ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
                    # sure shot arrow functions even if => is on new line
                (
                  (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
                  [(]\s*
                  (
                    ([)]\s*:) |                                                                                       # ():
                    ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
                  )
                ) |
                (
                  [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
                ) |
                # arrow function possible to detect only with => on same line
                (
                  (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
                  \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
                  (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
                  \s*=>                                                                                               # arrow operator
                )
                  ))
                )) |
                # typeannotation is fn type: < | () | (... | (param: | (param, | (param? | (param= | (param) =>
                (:\s*(
                  (<) |
                  ([(]\s*(
                    ([)]) |
                    (\.\.\.) |
                    ([_$[:alnum:]]+\s*(
                      ([:,?=])|
                      ([)]\s*=>)
                    ))
                  ))
                )) |
                (:\s*((<\s*$)|([\(]\s*([\{\[]\s*)?$))) |
                (:\s*(=>|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(<[^<>]*>)|[^<>(),=])+=\s*(
                  ((async\s+)?(
                    (function\s*[(<*]) |
                    (function\s+) |
                    ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
                  )) |
                  ((async\s*)?(
                    ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
                    # sure shot arrow functions even if => is on new line
                (
                  (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
                  [(]\s*
                  (
                    ([)]\s*:) |                                                                                       # ():
                    ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
                  )
                ) |
                (
                  [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
                ) |
                # arrow function possible to detect only with => on same line
                (
                  (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
                  \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
                  (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
                  \s*=>                                                                                               # arrow operator
                )
                  ))
                )))
              captures:
                1: meta.definition.property.tsx entity.name.function.tsx
                2: keyword.operator.optional.tsx
            - match: "[_$[:alpha:]][_$[:alnum:]]*"
              scope: meta.definition.property.tsx variable.object.property.tsx
            - match: \?
              scope: keyword.operator.optional.tsx
  for-loop:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))for(?=((\s+|(\s*\/\*([^\*]|(\*[^\/]))*\*\/\s*))await)?\s*(\/\*([^\*]|(\*[^\/]))*\*\/\s*)?(\())'
      captures:
        0: keyword.control.loop.tsx
      push:
        - match: (?<=\))
          pop: true
        - include: comment
        - match: await
          scope: keyword.control.loop.tsx
        - match: \(
          captures:
            0: meta.brace.round.tsx
          push:
            - match: \)
              captures:
                0: meta.brace.round.tsx
              pop: true
            - include: var-expr
            - include: expression
            - include: punctuation-semicolon
  function-body:
    - include: comment
    - include: type-parameters
    - include: function-parameters
    - include: return-type
    - include: decl-block
    - match: \*
      scope: keyword.generator.asterisk.tsx
  function-call:
    - match: '(?=(([_$[:alpha:]][_$[:alnum:]]*\s*\??\.\s*)*|(\??\.\s*)?)([_$[:alpha:]][_$[:alnum:]]*)\s*(\?\.\s*)?(<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>|\<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>)*(?!=)\>)*(?!=)>\s*)?\()'
      push:
        - match: '(?<=\))(?!(([_$[:alpha:]][_$[:alnum:]]*\s*\??\.\s*)*|(\??\.\s*)?)([_$[:alpha:]][_$[:alnum:]]*)\s*(\?\.\s*)?(<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>|\<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>)*(?!=)\>)*(?!=)>\s*)?\()'
          pop: true
        - match: '(?=(([_$[:alpha:]][_$[:alnum:]]*\s*\??\.\s*)*|(\??\.\s*)?)([_$[:alpha:]][_$[:alnum:]]*))'
          push:
            - meta_scope: meta.function-call.tsx
            - match: '(?=\s*(\?\.\s*)?(<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>|\<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>)*(?!=)\>)*(?!=)>\s*)?\()'
              pop: true
            - include: literal
            - include: support-objects
            - include: object-identifiers
            - include: punctuation-accessor
            - match: '(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))import(?=\s*[\(]\s*[\"\''\`]))'
              scope: keyword.operator.expression.import.tsx
            - match: "([_$[:alpha:]][_$[:alnum:]]*)"
              scope: entity.name.function.tsx
        - include: comment
        - match: \?\.
          scope: meta.function-call.tsx punctuation.accessor.optional.tsx
        - include: type-arguments
        - include: paren-expression
  function-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(export)\s+)?(?:(async)\s+)?(function\b)(?:\s*(\*))?(?:(?:\s+|(?<=\*))([_$[:alpha:]][_$[:alnum:]]*))?\s*'
      captures:
        1: keyword.control.export.tsx
        2: storage.modifier.async.tsx
        3: storage.type.function.tsx
        4: keyword.generator.asterisk.tsx
        5: meta.definition.function.tsx entity.name.function.tsx
      push:
        - meta_scope: meta.function.tsx
        - match: '(?=$|^|;)|(?<=\})'
          pop: true
        - include: function-name
        - include: function-body
  function-expression:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(async)\s+)?(function\b)(?:\s*(\*))?(?:(?:\s+|(?<=\*))([_$[:alpha:]][_$[:alnum:]]*))?\s*'
      captures:
        1: storage.modifier.async.tsx
        2: storage.type.function.tsx
        3: keyword.generator.asterisk.tsx
        4: meta.definition.function.tsx entity.name.function.tsx
      push:
        - meta_scope: meta.function.expression.tsx
        - match: '(?<=\})'
          pop: true
        - include: function-name
        - include: function-body
  function-name:
    - match: "[_$[:alpha:]][_$[:alnum:]]*"
      scope: meta.definition.function.tsx entity.name.function.tsx
  function-parameters:
    - match: \(
      captures:
        0: punctuation.definition.parameters.begin.tsx
      push:
        - meta_scope: meta.parameters.tsx
        - match: \)
          captures:
            0: punctuation.definition.parameters.end.tsx
          pop: true
        - include: function-parameters-body
  function-parameters-body:
    - include: comment
    - include: decorator
    - include: destructuring-parameter
    - include: parameter-name
    - include: type-annotation
    - include: variable-initializer
    - match: ","
      scope: punctuation.separator.parameter.tsx
  identifiers:
    - include: object-identifiers
    - match: |-
        (?x)(?:(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*)?([_$[:alpha:]][_$[:alnum:]]*)(?=\s*=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        ))
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: entity.name.function.tsx
    - match: '(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])'
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: variable.other.constant.property.tsx
    - match: '(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*([_$[:alpha:]][_$[:alnum:]]*)'
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: variable.other.property.tsx
    - match: "([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])"
      scope: variable.other.constant.tsx
    - match: "[_$[:alpha:]][_$[:alnum:]]*"
      scope: variable.other.readwrite.tsx
  import-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(import)(?!\s*[:\(])(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: keyword.control.export.tsx
        2: keyword.control.import.tsx
      push:
        - meta_scope: meta.import.tsx
        - match: '(?<!^import|[^\._$[:alnum:]]import)(?=;|$|^)'
          pop: true
        - include: comment
        - include: string
        - match: '(?<=^import|[^\._$[:alnum:]]import)(?!\s*["''])'
          push:
            - match: \bfrom\b
              captures:
                0: keyword.control.from.tsx
              pop: true
            - include: import-export-declaration
        - include: import-export-declaration
  import-equals-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(import)\s+([_$[:alpha:]][_$[:alnum:]]*)\s*(=)\s*(require)\s*(\()'
      captures:
        1: keyword.control.export.tsx
        2: keyword.control.import.tsx
        3: variable.other.readwrite.alias.tsx
        4: keyword.operator.assignment.tsx
        5: keyword.control.require.tsx
        6: meta.brace.round.tsx
      push:
        - meta_scope: meta.import-equals.external.tsx
        - match: \)
          captures:
            0: meta.brace.round.tsx
          pop: true
        - include: comment
        - include: string
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(import)\s+([_$[:alpha:]][_$[:alnum:]]*)\s*(=)\s*(?!require\b)'
      captures:
        1: keyword.control.export.tsx
        2: keyword.control.import.tsx
        3: variable.other.readwrite.alias.tsx
        4: keyword.operator.assignment.tsx
      push:
        - meta_scope: meta.import-equals.internal.tsx
        - match: (?=;|$|^)
          pop: true
        - include: comment
        - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))'
          captures:
            1: entity.name.type.module.tsx
            2: punctuation.accessor.tsx
            3: punctuation.accessor.optional.tsx
        - match: "([_$[:alpha:]][_$[:alnum:]]*)"
          scope: variable.other.readwrite.tsx
  import-export-block:
    - match: '\{'
      captures:
        0: punctuation.definition.block.tsx
      push:
        - meta_scope: meta.block.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - include: import-export-clause
  import-export-clause:
    - include: comment
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bdefault)|(\*)|(\b[_$[:alpha:]][_$[:alnum:]]*))\s+(as)\s+(?:(\bdefault(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.)))|(\b[_$[:alpha:]][_$[:alnum:]]*))'
      captures:
        1: keyword.control.default.tsx
        2: constant.language.import-export-all.tsx
        3: variable.other.readwrite.tsx
        4: keyword.control.as.tsx
        5: keyword.control.default.tsx
        6: variable.other.readwrite.alias.tsx
    - include: punctuation-comma
    - match: \*
      scope: constant.language.import-export-all.tsx
    - match: \b(default)\b
      scope: keyword.control.default.tsx
    - match: "([_$[:alpha:]][_$[:alnum:]]*)"
      scope: variable.other.readwrite.alias.tsx
  import-export-declaration:
    - include: comment
    - include: string
    - include: import-export-block
    - match: \bfrom\b
      scope: keyword.control.from.tsx
    - include: import-export-clause
  indexer-declaration:
    - match: '(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(readonly)\s*)?\s*(\[)\s*([_$[:alpha:]][_$[:alnum:]]*)\s*(?=:)'
      captures:
        1: storage.modifier.tsx
        2: meta.brace.square.tsx
        3: variable.parameter.tsx
      push:
        - meta_scope: meta.indexer.declaration.tsx
        - match: '(\])\s*(\?\s*)?|$'
          captures:
            1: meta.brace.square.tsx
            2: keyword.operator.optional.tsx
          pop: true
        - include: type-annotation
  indexer-mapped-type-declaration:
    - match: '(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))([+-])?(readonly)\s*)?\s*(\[)\s*([_$[:alpha:]][_$[:alnum:]]*)\s+(in)\s+'
      captures:
        1: keyword.operator.type.modifier.tsx
        2: storage.modifier.tsx
        3: meta.brace.square.tsx
        4: entity.name.type.tsx
        5: keyword.operator.expression.in.tsx
      push:
        - meta_scope: meta.indexer.mappedtype.declaration.tsx
        - match: '(\])([+-])?\s*(\?\s*)?|$'
          captures:
            1: meta.brace.square.tsx
            2: keyword.operator.type.modifier.tsx
            3: keyword.operator.optional.tsx
          pop: true
        - include: type
  inline-tags:
    - match: '(\[)[^\]]+(\])(?={@(?:link|linkcode|linkplain|tutorial))'
      scope: constant.other.description.jsdoc
      captures:
        1: punctuation.definition.bracket.square.begin.jsdoc
        2: punctuation.definition.bracket.square.end.jsdoc
    - match: '({)((@)(?:link(?:code|plain)?|tutorial))\s*'
      captures:
        1: punctuation.definition.bracket.curly.begin.jsdoc
        2: storage.type.class.jsdoc
        3: punctuation.definition.inline.tag.jsdoc
      push:
        - meta_scope: entity.name.type.instance.jsdoc
        - match: '}|(?=\*/)'
          captures:
            0: punctuation.definition.bracket.curly.end.jsdoc
          pop: true
        - match: '\G((?=https?://)(?:[^|}\s*]|\*[/])+)(\|)?'
          captures:
            1: variable.other.link.underline.jsdoc
            2: punctuation.separator.pipe.jsdoc
        - match: '\G((?:[^{}@\s|*]|\*[^/])+)(\|)?'
          captures:
            1: variable.other.description.jsdoc
            2: punctuation.separator.pipe.jsdoc
  instanceof-expr:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(instanceof)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: keyword.operator.expression.instanceof.tsx
      push:
        - match: '(?<=\))|(?=[;),}\]:?]|\|\||\&\&|$|((?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))function((\s+[_$[:alpha:]][_$[:alnum:]]*)|(\s*[\(]))))'
          pop: true
        - include: type
  interface-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(export)\s+)?\b(?:(abstract)\s+)?\b(interface)\b(?=\s+|/[/*])'
      captures:
        1: keyword.control.export.tsx
        2: storage.modifier.tsx
        3: storage.type.interface.tsx
      push:
        - meta_scope: meta.interface.tsx
        - match: '(?<=\})'
          pop: true
        - include: comment
        - include: class-or-interface-heritage
        - match: "[_$[:alpha:]][_$[:alnum:]]*"
          captures:
            0: entity.name.type.interface.tsx
        - include: type-parameters
        - include: class-or-interface-body
  jsdoctype:
    - match: '\G{(?:[^}*]|\*[^/}])+$'
      scope: invalid.illegal.type.jsdoc
    - match: '\G({)'
      captures:
        0: entity.name.type.instance.jsdoc
        1: punctuation.definition.bracket.curly.begin.jsdoc
      push:
        - meta_content_scope: entity.name.type.instance.jsdoc
        - match: '((}))\s*|(?=\*/)'
          captures:
            1: entity.name.type.instance.jsdoc
            2: punctuation.definition.bracket.curly.end.jsdoc
          pop: true
        - include: brackets
  jsx:
    - include: jsx-tag-without-attributes-in-expression
    - include: jsx-tag-in-expression
  jsx-children:
    - include: jsx-tag-without-attributes
    - include: jsx-tag
    - include: jsx-evaluated-code
    - include: jsx-entities
  jsx-entities:
    - match: "(&)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)"
      scope: constant.character.entity.tsx
      captures:
        1: punctuation.definition.entity.tsx
        3: punctuation.definition.entity.tsx
    - match: "&"
      scope: invalid.illegal.bad-ampersand.tsx
  jsx-evaluated-code:
    - match: '\{'
      captures:
        0: punctuation.section.embedded.begin.tsx
      push:
        - meta_scope: meta.embedded.expression.tsx
        - match: '\}'
          captures:
            0: punctuation.section.embedded.end.tsx
          pop: true
        - include: expression
  jsx-string-double-quoted:
    - match: '"'
      captures:
        0: punctuation.definition.string.begin.tsx
      push:
        - meta_scope: string.quoted.double.tsx
        - match: '"'
          captures:
            0: punctuation.definition.string.end.tsx
          pop: true
        - include: jsx-entities
  jsx-string-single-quoted:
    - match: "'"
      captures:
        0: punctuation.definition.string.begin.tsx
      push:
        - meta_scope: string.quoted.single.tsx
        - match: "'"
          captures:
            0: punctuation.definition.string.end.tsx
          pop: true
        - include: jsx-entities
  jsx-tag:
    - match: '(?=(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))(?=((<\s*)|(\s+))(?!\?)|\/?>))'
      push:
        - meta_scope: meta.tag.tsx
        - match: '(/>)|(?:(</)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))?\s*(>))'
          captures:
            1: punctuation.definition.tag.end.tsx
            2: punctuation.definition.tag.begin.tsx
            3: entity.name.tag.namespace.tsx
            4: punctuation.separator.namespace.tsx
            5: entity.name.tag.tsx
            6: support.class.component.tsx
            7: punctuation.definition.tag.end.tsx
          pop: true
        - match: '(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))(?=((<\s*)|(\s+))(?!\?)|\/?>)'
          captures:
            1: punctuation.definition.tag.begin.tsx
            2: entity.name.tag.namespace.tsx
            3: punctuation.separator.namespace.tsx
            4: entity.name.tag.tsx
            5: support.class.component.tsx
          push:
            - match: "(?=[/]?>)"
              pop: true
            - include: comment
            - include: type-arguments
            - include: jsx-tag-attributes
        - match: (>)
          captures:
            1: punctuation.definition.tag.end.tsx
          push:
            - meta_content_scope: meta.jsx.children.tsx
            - match: (?=</)
              pop: true
            - include: jsx-children
  jsx-tag-attribute-assignment:
    - match: '=(?=\s*(?:''|"|{|/\*|//|\n))'
      scope: keyword.operator.assignment.tsx
  jsx-tag-attribute-name:
    - match: |-
        (?x)
        \s*
        (?:([_$[:alpha:]][-$[:alnum:].]*)(:))?
        ([_$[:alpha:]][-$[:alnum:]]*)
        (?=\s|=|/?>|/\*|//)
      captures:
        1: entity.other.attribute-name.namespace.tsx
        2: punctuation.separator.namespace.tsx
        3: entity.other.attribute-name.tsx
  jsx-tag-attributes:
    - match: \s+
      push:
        - meta_scope: meta.tag.attributes.tsx
        - match: "(?=[/]?>)"
          pop: true
        - include: comment
        - include: jsx-tag-attribute-name
        - include: jsx-tag-attribute-assignment
        - include: jsx-string-double-quoted
        - include: jsx-string-single-quoted
        - include: jsx-evaluated-code
        - include: jsx-tag-attributes-illegal
  jsx-tag-attributes-illegal:
    - match: \S+
      scope: invalid.illegal.attribute.tsx
  jsx-tag-in-expression:
    - match: |-
        (?x)
        (?<!\+\+|--)(?<=[({\[,?=>:*]|&&|\|\||\?|^await|[^\._$[:alnum:]]await|^return|[^\._$[:alnum:]]return|^default|[^\._$[:alnum:]]default|^yield|[^\._$[:alnum:]]yield|^)\s*
        (?!<\s*[_$[:alpha:]][_$[:alnum:]]*((\s+extends\s+[^=>])|,)) # look ahead is not type parameter of arrow
        (?=(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))(?=((<\s*)|(\s+))(?!\?)|\/?>))
      push:
        - match: '(?!(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))(?=((<\s*)|(\s+))(?!\?)|\/?>))'
          pop: true
        - include: jsx-tag
  jsx-tag-without-attributes:
    - match: '(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))?\s*(>)'
      captures:
        1: punctuation.definition.tag.begin.tsx
        2: entity.name.tag.namespace.tsx
        3: punctuation.separator.namespace.tsx
        4: entity.name.tag.tsx
        5: support.class.component.tsx
        6: punctuation.definition.tag.end.tsx
      push:
        - meta_scope: meta.tag.without-attributes.tsx
        - meta_content_scope: meta.jsx.children.tsx
        - match: '(</)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))?\s*(>)'
          captures:
            1: punctuation.definition.tag.begin.tsx
            2: entity.name.tag.namespace.tsx
            3: punctuation.separator.namespace.tsx
            4: entity.name.tag.tsx
            5: support.class.component.tsx
            6: punctuation.definition.tag.end.tsx
          pop: true
        - include: jsx-children
  jsx-tag-without-attributes-in-expression:
    - match: '(?<!\+\+|--)(?<=[({\[,?=>:*]|&&|\|\||\?|^await|[^\._$[:alnum:]]await|^return|[^\._$[:alnum:]]return|^default|[^\._$[:alnum:]]default|^yield|[^\._$[:alnum:]]yield|^)\s*(?=(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))?\s*(>))'
      push:
        - match: '(?!(<)\s*(?:([_$[:alpha:]][-$[:alnum:].]*)(?<!\.|-)(:))?((?:[a-z][a-z0-9]*|([_$[:alpha:]][-$[:alnum:].]*))(?<!\.|-))?\s*(>))'
          pop: true
        - include: jsx-tag-without-attributes
  label:
    - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(:)(?=\s*\{)'
      captures:
        1: entity.name.label.tsx
        2: punctuation.separator.label.tsx
      push:
        - match: '(?<=\})'
          pop: true
        - include: decl-block
    - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(:)'
      captures:
        1: entity.name.label.tsx
        2: punctuation.separator.label.tsx
  literal:
    - include: numeric-literal
    - include: boolean-literal
    - include: null-literal
    - include: undefined-literal
    - include: numericConstant-literal
    - include: array-literal
    - include: this-literal
    - include: super-literal
  method-declaration:
    - match: '(?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:\b(public|private|protected)\s+)?(?:\b(abstract)\s+)?(?:\b(async)\s+)?\s*\b(constructor)\b(?!:)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: storage.modifier.tsx
        2: storage.modifier.tsx
        3: storage.modifier.async.tsx
        4: storage.type.tsx
      push:
        - meta_scope: meta.method.declaration.tsx
        - match: '(?=\}|;|,|$)|(?<=\})'
          pop: true
        - include: method-declaration-name
        - include: function-body
    - match: '(?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:\b(public|private|protected)\s+)?(?:\b(abstract)\s+)?(?:\b(async)\s+)?(?:(?:\s*\b(new)\b(?!:)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.)))|(?:(\*)\s*)?)(?=\s*[\(\<])'
      captures:
        1: storage.modifier.tsx
        2: storage.modifier.tsx
        3: storage.modifier.async.tsx
        4: keyword.operator.new.tsx
        5: keyword.generator.asterisk.tsx
      push:
        - meta_scope: meta.method.declaration.tsx
        - match: '(?=\}|;|,|$)|(?<=\})'
          pop: true
        - include: method-declaration-name
        - include: function-body
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:\b(public|private|protected)\s+)?(?:\b(abstract)\s+)?(?:\b(async)\s+)?(?:\b(get|set)\s+)?(?:(\*)\s*)?(?=\s*(((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\??))\s*[\(\<])
      captures:
        1: storage.modifier.tsx
        2: storage.modifier.tsx
        3: storage.modifier.async.tsx
        4: storage.type.property.tsx
        5: keyword.generator.asterisk.tsx
      push:
        - meta_scope: meta.method.declaration.tsx
        - match: '(?=\}|;|,|$)|(?<=\})'
          pop: true
        - include: method-declaration-name
        - include: function-body
  method-declaration-name:
    - match: |-
        (?x)(?=((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\??)\s*[\(\<])
      push:
        - match: (?=\(|\<)
          pop: true
        - include: string
        - include: array-literal
        - include: numeric-literal
        - match: "[_$[:alpha:]][_$[:alnum:]]*"
          scope: meta.definition.method.tsx entity.name.function.tsx
        - match: \?
          scope: keyword.operator.optional.tsx
  namespace-declaration:
    - match: '(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(namespace|module)\s+(?=[_$[:alpha:]"''`]))'
      captures:
        1: keyword.control.export.tsx
        2: storage.type.namespace.tsx
      push:
        - meta_scope: meta.namespace.declaration.tsx
        - match: '(?<=\})|(?=;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)'
          pop: true
        - include: comment
        - include: string
        - match: "([_$[:alpha:]][_$[:alnum:]]*)"
          scope: entity.name.type.module.tsx
        - include: punctuation-accessor
        - include: decl-block
  new-expr:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(new)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: keyword.operator.new.tsx
      push:
        - meta_scope: new.expr.tsx
        - match: '(?<=\))|(?=[;),}\]:]|\|\||\&\&|$|((?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))new(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.)))|((?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))function((\s+[_$[:alpha:]][_$[:alnum:]]*)|(\s*[\(]))))'
          pop: true
        - include: paren-expression
        - include: class-declaration
        - include: type
  null-literal:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))null(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: constant.language.null.tsx
  numeric-literal:
    - match: '\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$)'
      scope: constant.numeric.hex.tsx
    - match: '\b(?<!\$)0(b|B)[01][01_]*\b(?!\$)'
      scope: constant.numeric.binary.tsx
    - match: '\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$)'
      scope: constant.numeric.octal.tsx
    - match: |-
        (?x)
        (?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$)
      captures:
        0: constant.numeric.decimal.tsx
        1: meta.delimiter.decimal.period.tsx
        2: meta.delimiter.decimal.period.tsx
        3: meta.delimiter.decimal.period.tsx
        4: meta.delimiter.decimal.period.tsx
        5: meta.delimiter.decimal.period.tsx
        6: meta.delimiter.decimal.period.tsx
  numericConstant-literal:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))NaN(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: constant.language.nan.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))Infinity(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: constant.language.infinity.tsx
  object-binding-element:
    - include: comment
    - match: |-
        (?x)(?=((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(:))
      push:
        - match: '(?=,|\})'
          pop: true
        - include: object-binding-element-propertyName
        - include: binding-element
    - include: object-binding-pattern
    - include: destructuring-variable-rest
    - include: variable-initializer
    - include: punctuation-comma
  object-binding-element-const:
    - include: comment
    - match: |-
        (?x)(?=((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(:))
      push:
        - match: '(?=,|\})'
          pop: true
        - include: object-binding-element-propertyName
        - include: binding-element-const
    - include: object-binding-pattern-const
    - include: destructuring-variable-rest-const
    - include: variable-initializer
    - include: punctuation-comma
  object-binding-element-propertyName:
    - match: |-
        (?x)(?=((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(:))
      push:
        - match: (:)
          captures:
            0: punctuation.destructuring.tsx
          pop: true
        - include: string
        - include: array-literal
        - include: numeric-literal
        - match: "([_$[:alpha:]][_$[:alnum:]]*)"
          scope: variable.object.property.tsx
  object-binding-pattern:
    - match: '(?:(\.\.\.)\s*)?(\{)'
      captures:
        1: keyword.operator.rest.tsx
        2: punctuation.definition.binding-pattern.object.tsx
      push:
        - match: '\}'
          captures:
            0: punctuation.definition.binding-pattern.object.tsx
          pop: true
        - include: object-binding-element
  object-binding-pattern-const:
    - match: '(?:(\.\.\.)\s*)?(\{)'
      captures:
        1: keyword.operator.rest.tsx
        2: punctuation.definition.binding-pattern.object.tsx
      push:
        - match: '\}'
          captures:
            0: punctuation.definition.binding-pattern.object.tsx
          pop: true
        - include: object-binding-element-const
  object-identifiers:
    - match: '([_$[:alpha:]][_$[:alnum:]]*)(?=\s*\??\.\s*prototype\b(?!\$))'
      scope: support.class.tsx
    - match: |-
        (?x)(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*(?:
          ([[:upper:]][_$[:digit:][:upper:]]*) |
          ([_$[:alpha:]][_$[:alnum:]]*)
        )(?=\s*\??\.\s*[_$[:alpha:]][_$[:alnum:]]*)
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: variable.other.constant.object.property.tsx
        4: variable.other.object.property.tsx
    - match: |-
        (?x)(?:
          ([[:upper:]][_$[:digit:][:upper:]]*) |
          ([_$[:alpha:]][_$[:alnum:]]*)
        )(?=\s*\??\.\s*[_$[:alpha:]][_$[:alnum:]]*)
      captures:
        1: variable.other.constant.object.tsx
        2: variable.other.object.tsx
  object-literal:
    - match: '\{'
      captures:
        0: punctuation.definition.block.tsx
      push:
        - meta_scope: meta.objectliteral.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - include: object-member
  object-literal-method-declaration:
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:\b(async)\s+)?(?:\b(get|set)\s+)?(?:(\*)\s*)?(?=\s*(((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\??))\s*[\(\<])
      captures:
        1: storage.modifier.async.tsx
        2: storage.type.property.tsx
        3: keyword.generator.asterisk.tsx
      push:
        - meta_scope: meta.method.declaration.tsx
        - match: '(?=\}|;|,)|(?<=\})'
          pop: true
        - include: method-declaration-name
        - include: function-body
        - match: |-
            (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:\b(async)\s+)?(?:\b(get|set)\s+)?(?:(\*)\s*)?(?=\s*(((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
              (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
              (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
              (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
              (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
              (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
              (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
              (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
              (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
            )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(\??))\s*[\(\<])
          captures:
            1: storage.modifier.async.tsx
            2: storage.type.property.tsx
            3: keyword.generator.asterisk.tsx
          push:
            - match: (?=\(|\<)
              pop: true
            - include: method-declaration-name
  object-member:
    - include: comment
    - include: object-literal-method-declaration
    - match: '(?=\[)'
      push:
        - meta_scope: meta.object.member.tsx meta.object-literal.key.tsx
        - match: '(?=:)|((?<=[\]])(?=\s*[\(\<]))'
          pop: true
        - include: array-literal
    - match: '(?=[\''\"])'
      push:
        - meta_scope: meta.object.member.tsx meta.object-literal.key.tsx
        - match: '(?=:)|((?<=[\''\"])(?=\s*[\(\<]))'
          pop: true
        - include: string
    - match: '(?<=[\]\''\"])(?=\s*[\(\<])'
      push:
        - meta_scope: meta.method.declaration.tsx
        - match: '(?=\}|;|,)|(?<=\})'
          pop: true
        - include: function-body
    - match: '(?![_$[:alpha:]])([[:digit:]]+)\s*(?=:)'
      scope: meta.object.member.tsx
      captures:
        0: meta.object-literal.key.tsx
        1: constant.numeric.decimal.tsx
    - match: |-
        (?x)(?:([_$[:alpha:]][_$[:alnum:]]*)\s*(?=:\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )))
      scope: meta.object.member.tsx
      captures:
        0: meta.object-literal.key.tsx
        1: entity.name.function.tsx
    - match: '(?:[_$[:alpha:]][_$[:alnum:]]*)\s*(?=:)'
      scope: meta.object.member.tsx
      captures:
        0: meta.object-literal.key.tsx
    - match: \.\.\.
      captures:
        0: keyword.operator.spread.tsx
      push:
        - meta_scope: meta.object.member.tsx
        - match: '(?=,|\})'
          pop: true
        - include: expression
    - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(?=,|\}|$)'
      scope: meta.object.member.tsx
      captures:
        1: variable.other.readwrite.tsx
    - match: '(?=[_$[:alpha:]][_$[:alnum:]]*\s*=)'
      push:
        - meta_scope: meta.object.member.tsx
        - match: '(?=,|\}|$)'
          pop: true
        - include: expression
    - match: ":"
      captures:
        0: meta.object-literal.key.tsx punctuation.separator.key-value.tsx
      push:
        - meta_scope: meta.object.member.tsx
        - match: '(?=,|\})'
          pop: true
        - match: '(?<=:)\s*(async)?(?=\s*(<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)\(\s*([\{\[]\s*)?$)'
          captures:
            1: storage.modifier.async.tsx
          push:
            - match: (?<=\))
              pop: true
            - include: type-parameters
            - match: \(
              captures:
                0: meta.brace.round.tsx
              push:
                - match: \)
                  captures:
                    0: meta.brace.round.tsx
                  pop: true
                - include: expression-inside-possibly-arrow-parens
        - match: '(?<=:)\s*(async)?\s*(\()(?=\s*([\{\[]\s*)?$)'
          captures:
            1: storage.modifier.async.tsx
            2: meta.brace.round.tsx
          push:
            - match: \)
              captures:
                0: meta.brace.round.tsx
              pop: true
            - include: expression-inside-possibly-arrow-parens
        - include: expression
    - include: punctuation-comma
  parameter-array-binding-pattern:
    - match: '(?:(\.\.\.)\s*)?(\[)'
      captures:
        1: keyword.operator.rest.tsx
        2: punctuation.definition.binding-pattern.array.tsx
      push:
        - match: '\]'
          captures:
            0: punctuation.definition.binding-pattern.array.tsx
          pop: true
        - include: parameter-binding-element
        - include: punctuation-comma
  parameter-binding-element:
    - include: comment
    - include: string
    - include: parameter-object-binding-pattern
    - include: parameter-array-binding-pattern
    - include: destructuring-parameter-rest
    - include: variable-initializer
  parameter-name:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(public|protected|private|readonly)\s+(?=(public|protected|private|readonly)\s+)'
      captures:
        1: storage.modifier.tsx
    - match: |-
        (?x)(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(public|private|protected|readonly)\s+)?(?:(\.\.\.)\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))\s*(\??)(?=\s*
        # function assignment |
        (=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )) |
        # typeannotation is fn type: < | () | (... | (param: | (param, | (param? | (param= | (param) =>
        (:\s*(
          (<) |
          ([(]\s*(
            ([)]) |
            (\.\.\.) |
            ([_$[:alnum:]]+\s*(
              ([:,?=])|
              ([)]\s*=>)
            ))
          ))
        )) |
        (:\s*((<\s*$)|([\(]\s*([\{\[]\s*)?$))) |
        (:\s*(=>|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(<[^<>]*>)|[^<>(),=])+=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )))
      captures:
        1: storage.modifier.tsx
        2: keyword.operator.rest.tsx
        3: entity.name.function.tsx variable.language.this.tsx
        4: entity.name.function.tsx
        5: keyword.operator.optional.tsx
    - match: '(?x)(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(public|private|protected|readonly)\s+)?(?:(\.\.\.)\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))\s*(\??)'
      captures:
        1: storage.modifier.tsx
        2: keyword.operator.rest.tsx
        3: variable.parameter.tsx variable.language.this.tsx
        4: variable.parameter.tsx
        5: keyword.operator.optional.tsx
  parameter-object-binding-element:
    - include: comment
    - match: |-
        (?x)(?=((\b(?<!\$)0(x|X)[0-9a-fA-F][0-9a-fA-F_]*\b(?!\$))|(\b(?<!\$)0(b|B)[01][01_]*\b(?!\$))|(\b(?<!\$)0(o|O)?[0-7][0-7_]*\b(?!\$))|((?<!\$)(?:
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)| # 1.1E+3
          (?:\b[0-9][0-9_]*(\.)[eE][+-]?[0-9][0-9_]*\b)|             # 1.E+3
          (?:\B(\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|             # .1E+3
          (?:\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*\b)|                 # 1E+3
          (?:\b[0-9][0-9_]*(\.)[0-9][0-9_]*\b)|                      # 1.1
          (?:\b[0-9][0-9_]*(\.)\B)|                                  # 1.
          (?:\B(\.)[0-9][0-9_]*\b)|                                  # .1
          (?:\b[0-9][0-9_]*\b(?!\.))                                 # 1
        )(?!\$))|([_$[:alpha:]][_$[:alnum:]]*)|(\'([^\'\\]|\\\'|\\)*\')|(\"([^\"\\]|\\\"|\\)*\")|(\[([^\[\]]|\[[^\[\]]*\])+\]))\s*(:))
      push:
        - match: '(?=,|\})'
          pop: true
        - include: object-binding-element-propertyName
        - include: parameter-binding-element
    - include: parameter-object-binding-pattern
    - include: destructuring-parameter-rest
    - include: variable-initializer
    - include: punctuation-comma
  parameter-object-binding-pattern:
    - match: '(?:(\.\.\.)\s*)?(\{)'
      captures:
        1: keyword.operator.rest.tsx
        2: punctuation.definition.binding-pattern.object.tsx
      push:
        - match: '\}'
          captures:
            0: punctuation.definition.binding-pattern.object.tsx
          pop: true
        - include: parameter-object-binding-element
  paren-expression:
    - match: \(
      captures:
        0: meta.brace.round.tsx
      push:
        - match: \)
          captures:
            0: meta.brace.round.tsx
          pop: true
        - include: expression
  paren-expression-possibly-arrow:
    - match: '(?<=[(=,])\s*(async)?(?=\s*((<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*))?\(\s*[\{\[]\s*$)'
      captures:
        1: storage.modifier.async.tsx
      push:
        - match: (?<=\))
          pop: true
        - include: paren-expression-possibly-arrow-with-typeparameters
    - match: '(?<=[(=,]|=>)\s*(async)?(?=\s*((((<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*))?\()|(<))\s*$)'
      captures:
        1: storage.modifier.async.tsx
      push:
        - match: (?<=\))
          pop: true
        - include: paren-expression-possibly-arrow-with-typeparameters
    - include: possibly-arrow-return-type
  paren-expression-possibly-arrow-with-typeparameters:
    - include: type-parameters
    - match: \(
      captures:
        0: meta.brace.round.tsx
      push:
        - match: \)
          captures:
            0: meta.brace.round.tsx
          pop: true
        - include: expression-inside-possibly-arrow-parens
  possibly-arrow-return-type:
    - match: '(?<=\))\s*(:)(?=\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+\s*=>)'
      captures:
        1: meta.arrow.tsx meta.return.type.arrow.tsx keyword.operator.type.annotation.tsx
      push:
        - meta_content_scope: meta.arrow.tsx meta.return.type.arrow.tsx
        - match: '(?==>|\{|(^\s*(export|function|class|interface|let|var|const|import|enum|namespace|module|type|abstract|declare)\s+))'
          pop: true
        - include: arrow-return-type-body
  property-accessor:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(get|set)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: storage.type.property.tsx
  punctuation-accessor:
    - match: '(?:(\.)|(\?\.(?!\s*[[:digit:]])))'
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
  punctuation-comma:
    - match: ","
      scope: punctuation.separator.comma.tsx
  punctuation-semicolon:
    - match: ;
      scope: punctuation.terminator.statement.tsx
  qstring-double:
    - match: '"'
      captures:
        0: punctuation.definition.string.begin.tsx
      push:
        - meta_scope: string.quoted.double.tsx
        - match: '(")|((?:[^\\\n])$)'
          captures:
            1: punctuation.definition.string.end.tsx
            2: invalid.illegal.newline.tsx
          pop: true
        - include: string-character-escape
  qstring-single:
    - match: "'"
      captures:
        0: punctuation.definition.string.begin.tsx
      push:
        - meta_scope: string.quoted.single.tsx
        - match: '(\'')|((?:[^\\\n])$)'
          captures:
            1: punctuation.definition.string.end.tsx
            2: invalid.illegal.newline.tsx
          pop: true
        - include: string-character-escape
  regex:
    - match: '(?<!\+\+|--|})(?<=[=(:,\[?+!]|^return|[^\._$[:alnum:]]return|^case|[^\._$[:alnum:]]case|=>|&&|\|\||\*\/)\s*(\/)(?![\/*])(?=(?:[^\/\\\[]|\\.|\[([^\]\\]|\\.)+\])+\/[gimsuy]*(?!\s*[a-zA-Z0-9_$]))'
      captures:
        1: punctuation.definition.string.begin.tsx
      push:
        - meta_scope: string.regexp.tsx
        - match: "(/)([gimsuy]*)"
          captures:
            1: punctuation.definition.string.end.tsx
            2: keyword.other.tsx
          pop: true
        - include: regexp
    - match: '(?<![_$[:alnum:])\]]|\+\+|--|})\/(?![\/*])(?=(?:[^\/\\\[]|\\.|\[([^\]\\]|\\.)+\])+\/[gimsuy]*(?!\s*[a-zA-Z0-9_$]))'
      captures:
        0: punctuation.definition.string.begin.tsx
      push:
        - meta_scope: string.regexp.tsx
        - match: "(/)([gimsuy]*)"
          captures:
            1: punctuation.definition.string.end.tsx
            2: keyword.other.tsx
          pop: true
        - include: regexp
  regex-character-class:
    - match: '\\[wWsSdDtrnvf]|\.'
      scope: constant.other.character-class.regexp
    - match: '\\([0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4})'
      scope: constant.character.numeric.regexp
    - match: '\\c[A-Z]'
      scope: constant.character.control.regexp
    - match: \\.
      scope: constant.character.escape.backslash.regexp
  regexp:
    - match: '\\[bB]|\^|\$'
      scope: keyword.control.anchor.regexp
    - match: '\\[1-9]\d*|\\k<([a-zA-Z_$][\w$]*)>'
      captures:
        0: keyword.other.back-reference.regexp
        1: variable.other.regexp
    - match: '[?+*]|\{(\d+,\d+|\d+,|,\d+|\d+)\}\??'
      scope: keyword.operator.quantifier.regexp
    - match: \|
      scope: keyword.operator.or.regexp
    - match: (\()((\?=)|(\?!)|(\?<=)|(\?<!))
      captures:
        1: punctuation.definition.group.regexp
        2: punctuation.definition.group.assertion.regexp
        3: meta.assertion.look-ahead.regexp
        4: meta.assertion.negative-look-ahead.regexp
        5: meta.assertion.look-behind.regexp
        6: meta.assertion.negative-look-behind.regexp
      push:
        - meta_scope: meta.group.assertion.regexp
        - match: (\))
          captures:
            1: punctuation.definition.group.regexp
          pop: true
        - include: regexp
    - match: '\((?:(\?:)|(?:\?<([a-zA-Z_$][\w$]*)>))?'
      captures:
        0: punctuation.definition.group.regexp
        1: punctuation.definition.group.no-capture.regexp
        2: variable.other.regexp
      push:
        - meta_scope: meta.group.regexp
        - match: \)
          captures:
            0: punctuation.definition.group.regexp
          pop: true
        - include: regexp
    - match: '(\[)(\^)?'
      captures:
        1: punctuation.definition.character-class.regexp
        2: keyword.operator.negation.regexp
      push:
        - meta_scope: constant.other.character-class.set.regexp
        - match: '(\])'
          captures:
            1: punctuation.definition.character-class.regexp
          pop: true
        - match: '(?:.|(\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\c[A-Z])|(\\.))\-(?:[^\]\\]|(\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\c[A-Z])|(\\.))'
          scope: constant.other.character-class.range.regexp
          captures:
            1: constant.character.numeric.regexp
            2: constant.character.control.regexp
            3: constant.character.escape.backslash.regexp
            4: constant.character.numeric.regexp
            5: constant.character.control.regexp
            6: constant.character.escape.backslash.regexp
        - include: regex-character-class
    - include: regex-character-class
  return-type:
    - match: (?<=\))\s*(:)(?=\s*\S)
      captures:
        1: keyword.operator.type.annotation.tsx
      push:
        - meta_scope: meta.return.type.tsx
        - match: "(?<![:|&])(?=$|^|[{};,]|//)"
          pop: true
        - include: return-type-core
    - match: (?<=\))\s*(:)
      captures:
        1: keyword.operator.type.annotation.tsx
      push:
        - meta_scope: meta.return.type.tsx
        - match: '(?<![:|&])((?=[{};,]|//|^\s*$)|((?<=\S)(?=\s*$)))'
          pop: true
        - include: return-type-core
  return-type-core:
    - include: comment
    - match: '(?<=[:|&])(?=\s*\{)'
      push:
        - match: '(?<=\})'
          pop: true
        - include: type-object
    - include: type-predicate-operator
    - include: type
  statements:
    - include: string
    - include: template
    - include: comment
    - include: declaration
    - include: control-statement
    - include: after-operator-block-as-object-literal
    - include: decl-block
    - include: label
    - include: expression
    - include: punctuation-semicolon
  string:
    - include: qstring-single
    - include: qstring-double
  string-character-escape:
    - match: '\\(x[0-9A-Fa-f]{2}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)'
      scope: constant.character.escape.tsx
  super-literal:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))super\b(?!\$)'
      scope: variable.language.super.tsx
  support-objects:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(arguments)\b(?!\$)'
      scope: variable.language.arguments.tsx
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(Array|ArrayBuffer|Atomics|Boolean|DataView|Date|Float32Array|Float64Array|Function|Generator
        |GeneratorFunction|Int8Array|Int16Array|Int32Array|Intl|Map|Number|Object|Proxy
        |Reflect|RegExp|Set|SharedArrayBuffer|SIMD|String|Symbol|TypedArray
        |Uint8Array|Uint16Array|Uint32Array|Uint8ClampedArray|WeakMap|WeakSet)\b(?!\$)
      scope: support.class.builtin.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))((Eval|Internal|Range|Reference|Syntax|Type|URI)?Error)\b(?!\$)'
      scope: support.class.error.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(Promise)\b(?!\$)'
      scope: support.class.promise.tsx
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(clear(Interval|Timeout)|decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|escape|eval|
        isFinite|isNaN|parseFloat|parseInt|require|set(Interval|Timeout)|super|unescape|uneval)(?=\s*\()
      scope: support.function.tsx
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(Math)(?:\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*(?:
        (abs|acos|acosh|asin|asinh|atan|atan2|atanh|cbrt|ceil|clz32|cos|cosh|exp|
        expm1|floor|fround|hypot|imul|log|log10|log1p|log2|max|min|pow|random|
        round|sign|sin|sinh|sqrt|tan|tanh|trunc)
        |
        (E|LN10|LN2|LOG10E|LOG2E|PI|SQRT1_2|SQRT2)))?\b(?!\$)
      captures:
        1: support.constant.math.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
        4: support.function.math.tsx
        5: support.constant.property.math.tsx
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(console)(?:\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*(
        assert|clear|count|debug|dir|error|group|groupCollapsed|groupEnd|info|log
        |profile|profileEnd|table|time|timeEnd|timeStamp|trace|warn))?\b(?!\$)
      captures:
        1: support.class.console.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
        4: support.function.console.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(JSON)(?:\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*(parse|stringify))?\b(?!\$)'
      captures:
        1: support.constant.json.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
        4: support.function.json.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(import)\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*(meta)\b(?!\$)'
      captures:
        1: keyword.control.import.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
        4: support.variable.property.importmeta.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(new)\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))\s*(target)\b(?!\$)'
      captures:
        1: keyword.operator.new.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
        4: support.variable.property.target.tsx
    - match: |-
        (?x) (?:(\.)|(\?\.(?!\s*[[:digit:]]))) \s* (?:
        (constructor|length|prototype|__proto__)
        |
        (EPSILON|MAX_SAFE_INTEGER|MAX_VALUE|MIN_SAFE_INTEGER|MIN_VALUE|NEGATIVE_INFINITY|POSITIVE_INFINITY))\b(?!\$)
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: support.variable.property.tsx
        4: support.constant.tsx
    - match: |-
        (?x) (?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.)) \b (?:
        (document|event|navigator|performance|screen|window)
        |
        (AnalyserNode|ArrayBufferView|Attr|AudioBuffer|AudioBufferSourceNode|AudioContext|AudioDestinationNode|AudioListener
        |AudioNode|AudioParam|BatteryManager|BeforeUnloadEvent|BiquadFilterNode|Blob|BufferSource|ByteString|CSS|CSSConditionRule
        |CSSCounterStyleRule|CSSGroupingRule|CSSMatrix|CSSMediaRule|CSSPageRule|CSSPrimitiveValue|CSSRule|CSSRuleList|CSSStyleDeclaration
        |CSSStyleRule|CSSStyleSheet|CSSSupportsRule|CSSValue|CSSValueList|CanvasGradient|CanvasImageSource|CanvasPattern
        |CanvasRenderingContext2D|ChannelMergerNode|ChannelSplitterNode|CharacterData|ChromeWorker|CloseEvent|Comment|CompositionEvent
        |Console|ConvolverNode|Coordinates|Credential|CredentialsContainer|Crypto|CryptoKey|CustomEvent|DOMError|DOMException
        |DOMHighResTimeStamp|DOMImplementation|DOMString|DOMStringList|DOMStringMap|DOMTimeStamp|DOMTokenList|DataTransfer
        |DataTransferItem|DataTransferItemList|DedicatedWorkerGlobalScope|DelayNode|DeviceProximityEvent|DirectoryEntry
        |DirectoryEntrySync|DirectoryReader|DirectoryReaderSync|Document|DocumentFragment|DocumentTouch|DocumentType|DragEvent
        |DynamicsCompressorNode|Element|Entry|EntrySync|ErrorEvent|Event|EventListener|EventSource|EventTarget|FederatedCredential
        |FetchEvent|File|FileEntry|FileEntrySync|FileException|FileList|FileReader|FileReaderSync|FileSystem|FileSystemSync
        |FontFace|FormData|GainNode|Gamepad|GamepadButton|GamepadEvent|Geolocation|GlobalEventHandlers|HTMLAnchorElement
        |HTMLAreaElement|HTMLAudioElement|HTMLBRElement|HTMLBaseElement|HTMLBodyElement|HTMLButtonElement|HTMLCanvasElement
        |HTMLCollection|HTMLContentElement|HTMLDListElement|HTMLDataElement|HTMLDataListElement|HTMLDialogElement|HTMLDivElement
        |HTMLDocument|HTMLElement|HTMLEmbedElement|HTMLFieldSetElement|HTMLFontElement|HTMLFormControlsCollection|HTMLFormElement
        |HTMLHRElement|HTMLHeadElement|HTMLHeadingElement|HTMLHtmlElement|HTMLIFrameElement|HTMLImageElement|HTMLInputElement
        |HTMLKeygenElement|HTMLLIElement|HTMLLabelElement|HTMLLegendElement|HTMLLinkElement|HTMLMapElement|HTMLMediaElement
        |HTMLMetaElement|HTMLMeterElement|HTMLModElement|HTMLOListElement|HTMLObjectElement|HTMLOptGroupElement|HTMLOptionElement
        |HTMLOptionsCollection|HTMLOutputElement|HTMLParagraphElement|HTMLParamElement|HTMLPreElement|HTMLProgressElement
        |HTMLQuoteElement|HTMLScriptElement|HTMLSelectElement|HTMLShadowElement|HTMLSourceElement|HTMLSpanElement|HTMLStyleElement
        |HTMLTableCaptionElement|HTMLTableCellElement|HTMLTableColElement|HTMLTableDataCellElement|HTMLTableElement|HTMLTableHeaderCellElement
        |HTMLTableRowElement|HTMLTableSectionElement|HTMLTextAreaElement|HTMLTimeElement|HTMLTitleElement|HTMLTrackElement
        |HTMLUListElement|HTMLUnknownElement|HTMLVideoElement|HashChangeEvent|History|IDBCursor|IDBCursorWithValue|IDBDatabase
        |IDBEnvironment|IDBFactory|IDBIndex|IDBKeyRange|IDBMutableFile|IDBObjectStore|IDBOpenDBRequest|IDBRequest|IDBTransaction
        |IDBVersionChangeEvent|IIRFilterNode|IdentityManager|ImageBitmap|ImageBitmapFactories|ImageData|Index|InputDeviceCapabilities
        |InputEvent|InstallEvent|InstallTrigger|KeyboardEvent|LinkStyle|LocalFileSystem|LocalFileSystemSync|Location|MIDIAccess
        |MIDIConnectionEvent|MIDIInput|MIDIInputMap|MIDIOutputMap|MediaElementAudioSourceNode|MediaError|MediaKeyMessageEvent
        |MediaKeySession|MediaKeyStatusMap|MediaKeySystemAccess|MediaKeySystemConfiguration|MediaKeys|MediaRecorder|MediaStream
        |MediaStreamAudioDestinationNode|MediaStreamAudioSourceNode|MessageChannel|MessageEvent|MessagePort|MouseEvent
        |MutationObserver|MutationRecord|NamedNodeMap|Navigator|NavigatorConcurrentHardware|NavigatorGeolocation|NavigatorID
        |NavigatorLanguage|NavigatorOnLine|Node|NodeFilter|NodeIterator|NodeList|NonDocumentTypeChildNode|Notification
        |OfflineAudioCompletionEvent|OfflineAudioContext|OscillatorNode|PageTransitionEvent|PannerNode|ParentNode|PasswordCredential
        |Path2D|PaymentAddress|PaymentRequest|PaymentResponse|Performance|PerformanceEntry|PerformanceFrameTiming|PerformanceMark
        |PerformanceMeasure|PerformanceNavigation|PerformanceNavigationTiming|PerformanceObserver|PerformanceObserverEntryList
        |PerformanceResourceTiming|PerformanceTiming|PeriodicSyncEvent|PeriodicWave|Plugin|Point|PointerEvent|PopStateEvent
        |PortCollection|Position|PositionError|PositionOptions|PresentationConnectionClosedEvent|PresentationConnectionList
        |PresentationReceiver|ProcessingInstruction|ProgressEvent|PromiseRejectionEvent|PushEvent|PushRegistrationManager
        |RTCCertificate|RTCConfiguration|RTCPeerConnection|RTCSessionDescriptionCallback|RTCStatsReport|RadioNodeList|RandomSource
        |Range|ReadableByteStream|RenderingContext|SVGAElement|SVGAngle|SVGAnimateColorElement|SVGAnimateElement|SVGAnimateMotionElement
        |SVGAnimateTransformElement|SVGAnimatedAngle|SVGAnimatedBoolean|SVGAnimatedEnumeration|SVGAnimatedInteger|SVGAnimatedLength
        |SVGAnimatedLengthList|SVGAnimatedNumber|SVGAnimatedNumberList|SVGAnimatedPoints|SVGAnimatedPreserveAspectRatio
        |SVGAnimatedRect|SVGAnimatedString|SVGAnimatedTransformList|SVGAnimationElement|SVGCircleElement|SVGClipPathElement
        |SVGCursorElement|SVGDefsElement|SVGDescElement|SVGElement|SVGEllipseElement|SVGEvent|SVGFilterElement|SVGFontElement
        |SVGFontFaceElement|SVGFontFaceFormatElement|SVGFontFaceNameElement|SVGFontFaceSrcElement|SVGFontFaceUriElement
        |SVGForeignObjectElement|SVGGElement|SVGGlyphElement|SVGGradientElement|SVGHKernElement|SVGImageElement|SVGLength
        |SVGLengthList|SVGLineElement|SVGLinearGradientElement|SVGMPathElement|SVGMaskElement|SVGMatrix|SVGMissingGlyphElement
        |SVGNumber|SVGNumberList|SVGPathElement|SVGPatternElement|SVGPoint|SVGPolygonElement|SVGPolylineElement|SVGPreserveAspectRatio
        |SVGRadialGradientElement|SVGRect|SVGRectElement|SVGSVGElement|SVGScriptElement|SVGSetElement|SVGStopElement|SVGStringList
        |SVGStylable|SVGStyleElement|SVGSwitchElement|SVGSymbolElement|SVGTRefElement|SVGTSpanElement|SVGTests|SVGTextElement
        |SVGTextPositioningElement|SVGTitleElement|SVGTransform|SVGTransformList|SVGTransformable|SVGUseElement|SVGVKernElement
        |SVGViewElement|ServiceWorker|ServiceWorkerContainer|ServiceWorkerGlobalScope|ServiceWorkerRegistration|ServiceWorkerState
        |ShadowRoot|SharedWorker|SharedWorkerGlobalScope|SourceBufferList|StereoPannerNode|Storage|StorageEvent|StyleSheet
        |StyleSheetList|SubtleCrypto|SyncEvent|Text|TextMetrics|TimeEvent|TimeRanges|Touch|TouchEvent|TouchList|Transferable
        |TreeWalker|UIEvent|USVString|VRDisplayCapabilities|ValidityState|WaveShaperNode|WebGL|WebGLActiveInfo|WebGLBuffer
        |WebGLContextEvent|WebGLFramebuffer|WebGLProgram|WebGLRenderbuffer|WebGLRenderingContext|WebGLShader|WebGLShaderPrecisionFormat
        |WebGLTexture|WebGLTimerQueryEXT|WebGLTransformFeedback|WebGLUniformLocation|WebGLVertexArrayObject|WebGLVertexArrayObjectOES
        |WebSocket|WebSockets|WebVTT|WheelEvent|Window|WindowBase64|WindowEventHandlers|WindowTimers|Worker|WorkerGlobalScope
        |WorkerLocation|WorkerNavigator|XMLHttpRequest|XMLHttpRequestEventTarget|XMLSerializer|XPathExpression|XPathResult
        |XSLTProcessor))\b(?!\$)
      captures:
        1: support.variable.dom.tsx
        2: support.class.dom.tsx
    - match: |-
        (?x) (?:(\.)|(\?\.(?!\s*[[:digit:]]))) \s* (?:
        (ATTRIBUTE_NODE|CDATA_SECTION_NODE|COMMENT_NODE|DOCUMENT_FRAGMENT_NODE|DOCUMENT_NODE|DOCUMENT_TYPE_NODE
        |DOMSTRING_SIZE_ERR|ELEMENT_NODE|ENTITY_NODE|ENTITY_REFERENCE_NODE|HIERARCHY_REQUEST_ERR|INDEX_SIZE_ERR
        |INUSE_ATTRIBUTE_ERR|INVALID_CHARACTER_ERR|NO_DATA_ALLOWED_ERR|NO_MODIFICATION_ALLOWED_ERR|NOT_FOUND_ERR
        |NOT_SUPPORTED_ERR|NOTATION_NODE|PROCESSING_INSTRUCTION_NODE|TEXT_NODE|WRONG_DOCUMENT_ERR)
        |
        (_content|[xyz]|abbr|above|accept|acceptCharset|accessKey|action|align|[av]Link(?:color)?|all|alt|anchors|appCodeName
        |appCore|applets|appMinorVersion|appName|appVersion|archive|areas|arguments|attributes|availHeight|availLeft|availTop
        |availWidth|axis|background|backgroundColor|backgroundImage|below|bgColor|body|border|borderBottomWidth|borderColor
        |borderLeftWidth|borderRightWidth|borderStyle|borderTopWidth|borderWidth|bottom|bufferDepth|callee|caller|caption
        |cellPadding|cells|cellSpacing|ch|characterSet|charset|checked|childNodes|chOff|cite|classes|className|clear
        |clientInformation|clip|clipBoardData|closed|code|codeBase|codeType|color|colorDepth|cols|colSpan|compact|complete
        |components|content|controllers|cookie|cookieEnabled|cords|cpuClass|crypto|current|data|dateTime|declare|defaultCharset
        |defaultChecked|defaultSelected|defaultStatus|defaultValue|defaultView|defer|description|dialogArguments|dialogHeight
        |dialogLeft|dialogTop|dialogWidth|dir|directories|disabled|display|docmain|doctype|documentElement|elements|embeds
        |enabledPlugin|encoding|enctype|entities|event|expando|external|face|fgColor|filename|firstChild|fontFamily|fontSize
        |fontWeight|form|formName|forms|frame|frameBorder|frameElement|frames|hasFocus|hash|headers|height|history|host
        |hostname|href|hreflang|hspace|htmlFor|httpEquiv|id|ids|ignoreCase|images|implementation|index|innerHeight|innerWidth
        |input|isMap|label|lang|language|lastChild|lastIndex|lastMatch|lastModified|lastParen|layer[sXY]|left|leftContext
        |lineHeight|link|linkColor|links|listStyleType|localName|location|locationbar|longDesc|lowsrc|lowSrc|marginBottom
        |marginHeight|marginLeft|marginRight|marginTop|marginWidth|maxLength|media|menubar|method|mimeTypes|multiline|multiple
        |name|nameProp|namespaces|namespaceURI|next|nextSibling|nodeName|nodeType|nodeValue|noHref|noResize|noShade|notationName
        |notations|noWrap|object|offscreenBuffering|onLine|onreadystatechange|opener|opsProfile|options|oscpu|outerHeight
        |outerWidth|ownerDocument|paddingBottom|paddingLeft|paddingRight|paddingTop|page[XY]|page[XY]Offset|parent|parentLayer
        |parentNode|parentWindow|pathname|personalbar|pixelDepth|pkcs11|platform|plugins|port|prefix|previous|previousDibling
        |product|productSub|profile|profileend|prompt|prompter|protocol|publicId|readOnly|readyState|referrer|rel|responseText
        |responseXML|rev|right|rightContext|rowIndex|rows|rowSpan|rules|scheme|scope|screen[XY]|screenLeft|screenTop|scripts
        |scrollbars|scrolling|sectionRowIndex|security|securityPolicy|selected|selectedIndex|selection|self|shape|siblingAbove
        |siblingBelow|size|source|specified|standby|start|status|statusbar|statusText|style|styleSheets|suffixes|summary
        |systemId|systemLanguage|tagName|tags|target|tBodies|text|textAlign|textDecoration|textIndent|textTransform|tFoot|tHead
        |title|toolbar|top|type|undefined|uniqueID|updateInterval|URL|URLUnencoded|useMap|userAgent|userLanguage|userProfile
        |vAlign|value|valueType|vendor|vendorSub|version|visibility|vspace|whiteSpace|width|X[MS]LDocument|zIndex))\b(?!\$|\s*(<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?\()
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: support.constant.dom.tsx
        4: support.variable.property.dom.tsx
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(Buffer|EventEmitter|Server|Pipe|Socket|REPLServer|ReadStream|WriteStream|Stream
        |Inflate|Deflate|InflateRaw|DeflateRaw|GZip|GUnzip|Unzip|Zip)\b(?!\$)
      scope: support.class.node.tsx
    - match: |-
        (?x)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(process)(?:(?:(\.)|(\?\.(?!\s*[[:digit:]])))(?:
          (arch|argv|config|connected|env|execArgv|execPath|exitCode|mainModule|pid|platform|release|stderr|stdin|stdout|title|version|versions)
          |
          (abort|chdir|cwd|disconnect|exit|[sg]ete?[gu]id|send|[sg]etgroups|initgroups|kill|memoryUsage|nextTick|umask|uptime|hrtime)
        ))?\b(?!\$)
      captures:
        1: support.variable.object.process.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
        4: support.variable.property.process.tsx
        5: support.function.process.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(exports)|(module)(?:(?:(\.)|(\?\.(?!\s*[[:digit:]])))(exports|id|filename|loaded|parent|children))?)\b(?!\$)'
      captures:
        1: support.type.object.module.tsx
        2: support.type.object.module.tsx
        3: punctuation.accessor.tsx
        4: punctuation.accessor.optional.tsx
        5: support.type.object.module.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(global|GLOBAL|root|__dirname|__filename)\b(?!\$)'
      scope: support.variable.object.node.tsx
    - match: |-
        (?x) (?:(\.)|(\?\.(?!\s*[[:digit:]]))) \s*
        (?:
         (on(?:Rowsinserted|Rowsdelete|Rowenter|Rowexit|Resize|Resizestart|Resizeend|Reset|
           Readystatechange|Mouseout|Mouseover|Mousedown|Mouseup|Mousemove|
           Before(?:cut|deactivate|unload|update|paste|print|editfocus|activate)|
           Blur|Scrolltop|Submit|Select|Selectstart|Selectionchange|Hover|Help|
           Change|Contextmenu|Controlselect|Cut|Cellchange|Clock|Close|Deactivate|
           Datasetchanged|Datasetcomplete|Dataavailable|Drop|Drag|Dragstart|Dragover|
           Dragdrop|Dragenter|Dragend|Dragleave|Dblclick|Unload|Paste|Propertychange|Error|
           Errorupdate|Keydown|Keyup|Keypress|Focus|Load|Activate|Afterupdate|Afterprint|Abort)
         ) |
         (shift|showModelessDialog|showModalDialog|showHelp|scroll|scrollX|scrollByPages|
           scrollByLines|scrollY|scrollTo|stop|strike|sizeToContent|sidebar|signText|sort|
           sup|sub|substr|substring|splice|split|send|set(?:Milliseconds|Seconds|Minutes|Hours|
           Month|Year|FullYear|Date|UTC(?:Milliseconds|Seconds|Minutes|Hours|Month|FullYear|Date)|
           Time|Hotkeys|Cursor|ZOptions|Active|Resizable|RequestHeader)|search|slice|
           savePreferences|small|home|handleEvent|navigate|char|charCodeAt|charAt|concat|
           contextual|confirm|compile|clear|captureEvents|call|createStyleSheet|createPopup|
           createEventObject|to(?:GMTString|UTCString|String|Source|UpperCase|LowerCase|LocaleString)|
           test|taint|taintEnabled|indexOf|italics|disableExternalCapture|dump|detachEvent|unshift|
           untaint|unwatch|updateCommands|join|javaEnabled|pop|push|plugins.refresh|paddings|parse|
           print|prompt|preference|enableExternalCapture|exec|execScript|valueOf|UTC|find|file|
           fileModifiedDate|fileSize|fileCreatedDate|fileUpdatedDate|fixed|fontsize|fontcolor|
           forward|fromCharCode|watch|link|load|lastIndexOf|anchor|attachEvent|atob|apply|alert|
           abort|routeEvents|resize|resizeBy|resizeTo|recalc|returnValue|replace|reverse|reload|
           releaseCapture|releaseEvents|go|get(?:Milliseconds|Seconds|Minutes|Hours|Month|Day|Year|FullYear|
           Time|Date|TimezoneOffset|UTC(?:Milliseconds|Seconds|Minutes|Hours|Day|Month|FullYear|Date)|
           Attention|Selection|ResponseHeader|AllResponseHeaders)|moveBy|moveBelow|moveTo|
           moveToAbsolute|moveAbove|mergeAttributes|match|margins|btoa|big|bold|borderWidths|blink|back
         ) |
         (acceptNode|add|addEventListener|addTextTrack|adoptNode|after|animate|append|
           appendChild|appendData|before|blur|canPlayType|captureStream|
           caretPositionFromPoint|caretRangeFromPoint|checkValidity|clear|click|
           cloneContents|cloneNode|cloneRange|close|closest|collapse|
           compareBoundaryPoints|compareDocumentPosition|comparePoint|contains|
           convertPointFromNode|convertQuadFromNode|convertRectFromNode|createAttribute|
           createAttributeNS|createCaption|createCDATASection|createComment|
           createContextualFragment|createDocument|createDocumentFragment|
           createDocumentType|createElement|createElementNS|createEntityReference|
           createEvent|createExpression|createHTMLDocument|createNodeIterator|
           createNSResolver|createProcessingInstruction|createRange|createShadowRoot|
           createTBody|createTextNode|createTFoot|createTHead|createTreeWalker|delete|
           deleteCaption|deleteCell|deleteContents|deleteData|deleteRow|deleteTFoot|
           deleteTHead|detach|disconnect|dispatchEvent|elementFromPoint|elementsFromPoint|
           enableStyleSheetsForSet|entries|evaluate|execCommand|exitFullscreen|
           exitPointerLock|expand|extractContents|fastSeek|firstChild|focus|forEach|get|
           getAll|getAnimations|getAttribute|getAttributeNames|getAttributeNode|
           getAttributeNodeNS|getAttributeNS|getBoundingClientRect|getBoxQuads|
           getClientRects|getContext|getDestinationInsertionPoints|getElementById|
           getElementsByClassName|getElementsByName|getElementsByTagName|
           getElementsByTagNameNS|getItem|getNamedItem|getSelection|getStartDate|
           getVideoPlaybackQuality|has|hasAttribute|hasAttributeNS|hasAttributes|
           hasChildNodes|hasFeature|hasFocus|importNode|initEvent|insertAdjacentElement|
           insertAdjacentHTML|insertAdjacentText|insertBefore|insertCell|insertData|
           insertNode|insertRow|intersectsNode|isDefaultNamespace|isEqualNode|
           isPointInRange|isSameNode|item|key|keys|lastChild|load|lookupNamespaceURI|
           lookupPrefix|matches|move|moveAttribute|moveAttributeNode|moveChild|
           moveNamedItem|namedItem|nextNode|nextSibling|normalize|observe|open|
           parentNode|pause|play|postMessage|prepend|preventDefault|previousNode|
           previousSibling|probablySupportsContext|queryCommandEnabled|
           queryCommandIndeterm|queryCommandState|queryCommandSupported|queryCommandValue|
           querySelector|querySelectorAll|registerContentHandler|registerElement|
           registerProtocolHandler|releaseCapture|releaseEvents|remove|removeAttribute|
           removeAttributeNode|removeAttributeNS|removeChild|removeEventListener|
           removeItem|replace|replaceChild|replaceData|replaceWith|reportValidity|
           requestFullscreen|requestPointerLock|reset|scroll|scrollBy|scrollIntoView|
           scrollTo|seekToNextFrame|select|selectNode|selectNodeContents|set|setAttribute|
           setAttributeNode|setAttributeNodeNS|setAttributeNS|setCapture|
           setCustomValidity|setEnd|setEndAfter|setEndBefore|setItem|setNamedItem|
           setRangeText|setSelectionRange|setSinkId|setStart|setStartAfter|setStartBefore|
           slice|splitText|stepDown|stepUp|stopImmediatePropagation|stopPropagation|
           submit|substringData|supports|surroundContents|takeRecords|terminate|toBlob|
           toDataURL|toggle|toString|values|write|writeln
         ) |
         (all|catch|finally|race|reject|resolve|then
         )
        )(?=\s*\()
      captures:
        1: punctuation.accessor.tsx
        2: punctuation.accessor.optional.tsx
        3: support.function.event-handler.tsx
        4: support.function.tsx
        5: support.function.dom.tsx
        6: support.function.promise.tsx
  switch-statement:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?=\bswitch\s*\()'
      push:
        - meta_scope: switch-statement.expr.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(switch)\s*(\()'
          captures:
            1: keyword.control.switch.tsx
            2: meta.brace.round.tsx
          push:
            - meta_scope: switch-expression.expr.tsx
            - match: \)
              captures:
                0: meta.brace.round.tsx
              pop: true
            - include: expression
        - match: '\{'
          captures:
            0: punctuation.definition.block.tsx
          push:
            - meta_scope: switch-block.expr.tsx
            - match: '(?=\})'
              pop: true
            - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(case|default(?=:))(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
              captures:
                1: keyword.control.switch.tsx
              push:
                - meta_scope: case-clause.expr.tsx
                - match: (?=:)
                  pop: true
                - include: expression
            - match: '(:)\s*(\{)'
              captures:
                1: case-clause.expr.tsx punctuation.definition.section.case-statement.tsx
                2: meta.block.tsx punctuation.definition.block.tsx
              push:
                - meta_content_scope: meta.block.tsx
                - match: '\}'
                  captures:
                    0: meta.block.tsx punctuation.definition.block.tsx
                  pop: true
                - include: statements
            - match: (:)
              captures:
                0: case-clause.expr.tsx punctuation.definition.section.case-statement.tsx
            - include: statements
  template:
    - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(?=(<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>|\<\s*(((keyof|infer)\s+)|(([_$[:alpha:]][_$[:alnum:]]*|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\''[^\'']*\'')|(\"[^\"]*\")|(\`[^\`]*\`))(?=\s*([\<\>\,\.\[=]|&(?!&)|\|(?!\|)))))([^<>\(]|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(?<==)\>)*(?!=)\>)*(?!=)>\s*)`)'
      captures:
        1: entity.name.function.tagged-template.tsx
      push:
        - meta_scope: string.template.tsx
        - match: (?=`)
          pop: true
        - include: type-arguments
    - match: "([_$[:alpha:]][_$[:alnum:]]*)?(`)"
      captures:
        1: entity.name.function.tagged-template.tsx
        2: punctuation.definition.string.template.begin.tsx
      push:
        - meta_scope: string.template.tsx
        - match: "`"
          captures:
            0: punctuation.definition.string.template.end.tsx
          pop: true
        - include: template-substitution-element
        - include: string-character-escape
  template-substitution-element:
    - match: '\$\{'
      captures:
        0: punctuation.definition.template-expression.begin.tsx
      push:
        - meta_scope: meta.template.expression.tsx
        - meta_content_scope: meta.embedded.line.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.template-expression.end.tsx
          pop: true
        - include: expression
  ternary-expression:
    - match: '(?!\?\.\s*[^[:digit:]])(\?)'
      captures:
        1: keyword.operator.ternary.tsx
      push:
        - match: \s*(:)
          captures:
            1: keyword.operator.ternary.tsx
          pop: true
        - include: expression
  this-literal:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))this\b(?!\$)'
      scope: variable.language.this.tsx
  type:
    - include: comment
    - include: string
    - include: numeric-literal
    - include: type-primitive
    - include: type-builtin-literals
    - include: type-parameters
    - include: type-tuple
    - include: type-object
    - include: type-conditional
    - include: type-operators
    - include: type-fn-type-parameters
    - include: type-paren-or-function-parameters
    - include: type-function-return-type
    - include: type-name
  type-alias-declaration:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(type)\b\s+([_$[:alpha:]][_$[:alnum:]]*)\s*'
      captures:
        1: keyword.control.export.tsx
        2: storage.type.type.tsx
        3: entity.name.type.alias.tsx
      push:
        - meta_scope: meta.type.declaration.tsx
        - match: '(?=\}|;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)'
          pop: true
        - include: comment
        - include: type-parameters
        - match: (=)\s*
          captures:
            1: keyword.operator.assignment.tsx
          push:
            - match: '(?=\}|;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)'
              pop: true
            - include: type
  type-annotation:
    - match: (:)(?=\s*\S)
      captures:
        1: keyword.operator.type.annotation.tsx
      push:
        - meta_scope: meta.type.annotation.tsx
        - match: '(?<![:|&])((?=$|^|[,);\}\]]|//)|(?==[^>])|((?<=[\}>\]\)]|[_$[:alpha:]])\s*(?=\{)))'
          pop: true
        - include: type
    - match: (:)
      captures:
        1: keyword.operator.type.annotation.tsx
      push:
        - meta_scope: meta.type.annotation.tsx
        - match: '(?<![:|&])((?=[,);\}\]]|//)|(?==[^>])|(?=^\s*$)|((?<=\S)(?=\s*$))|((?<=[\}>\]\)]|[_$[:alpha:]])\s*(?=\{)))'
          pop: true
        - include: type
  type-arguments:
    - match: \<
      captures:
        0: punctuation.definition.typeparameters.begin.tsx
      push:
        - meta_scope: meta.type.parameters.tsx
        - match: \>
          captures:
            0: punctuation.definition.typeparameters.end.tsx
          pop: true
        - include: type
        - include: punctuation-comma
  type-builtin-literals:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(this|true|false|undefined|null|object)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: support.type.builtin.tsx
  type-conditional:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(extends)\s+'
      captures:
        1: storage.modifier.tsx
      push:
        - match: (?<=:)
          pop: true
        - match: \?
          captures:
            0: keyword.operator.ternary.tsx
          push:
            - match: ":"
              captures:
                0: keyword.operator.ternary.tsx
              pop: true
            - include: type
        - include: type
  type-fn-type-parameters:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(new)\b(?=\s*\<)'
      scope: meta.type.constructor.tsx
      captures:
        1: keyword.control.new.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(new)\b\s*(?=\()'
      captures:
        1: keyword.control.new.tsx
      push:
        - meta_scope: meta.type.constructor.tsx
        - match: (?<=\))
          pop: true
        - include: function-parameters
    - match: |-
        (?x)(
          (?=
            [(]\s*(
              ([)]) |
              (\.\.\.) |
              ([_$[:alnum:]]+\s*(
                ([:,?=])|
                ([)]\s*=>)
              ))
            )
          )
        )
      push:
        - meta_scope: meta.type.function.tsx
        - match: (?<=\))
          pop: true
        - include: function-parameters
  type-function-return-type:
    - match: (=>)(?=\s*\S)
      captures:
        1: storage.type.function.arrow.tsx
      push:
        - meta_scope: meta.type.function.return.tsx
        - match: '(?<!=>)(?<![|&])(?=[,\]\)\{\}=;>:\?]|//|$)'
          pop: true
        - include: type-function-return-type-core
    - match: "=>"
      captures:
        0: storage.type.function.arrow.tsx
      push:
        - meta_scope: meta.type.function.return.tsx
        - match: '(?<!=>)(?<![|&])((?=[,\]\)\{\}=;:\?>]|//|^\s*$)|((?<=\S)(?=\s*$)))'
          pop: true
        - include: type-function-return-type-core
  type-function-return-type-core:
    - include: comment
    - match: '(?<==>)(?=\s*\{)'
      push:
        - match: '(?<=\})'
          pop: true
        - include: type-object
    - include: type-predicate-operator
    - include: type
  type-name:
    - match: '([_$[:alpha:]][_$[:alnum:]]*)\s*(?:(\.)|(\?\.(?!\s*[[:digit:]])))'
      captures:
        1: entity.name.type.module.tsx
        2: punctuation.accessor.tsx
        3: punctuation.accessor.optional.tsx
    - match: "[_$[:alpha:]][_$[:alnum:]]*"
      scope: entity.name.type.tsx
  type-object:
    - match: '\{'
      captures:
        0: punctuation.definition.block.tsx
      push:
        - meta_scope: meta.object.type.tsx
        - match: '\}'
          captures:
            0: punctuation.definition.block.tsx
          pop: true
        - include: comment
        - include: method-declaration
        - include: indexer-declaration
        - include: indexer-mapped-type-declaration
        - include: field-declaration
        - include: type-annotation
        - match: \.\.\.
          captures:
            0: keyword.operator.spread.tsx
          push:
            - match: '(?=\}|;|,|$)|(?<=\})'
              pop: true
            - include: type
        - include: punctuation-comma
        - include: punctuation-semicolon
        - include: type
  type-operators:
    - include: typeof-operator
    - match: '(?:([&|])|(=(?!>)))(?=\s*\{)'
      captures:
        1: keyword.operator.type.tsx
        2: keyword.operator.assignment.tsx
      push:
        - match: '(?<=\})'
          pop: true
        - include: type-object
    - match: "([&|])|(=(?!>))"
      captures:
        1: keyword.operator.type.tsx
        2: keyword.operator.assignment.tsx
      push:
        - match: (?=\S)
          pop: true
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))keyof(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.expression.keyof.tsx
    - match: (\?|\:)
      scope: keyword.operator.ternary.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))infer(?=\s+[_$[:alpha:]])'
      scope: keyword.operator.expression.infer.tsx
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))import(?=\s*\()'
      scope: keyword.operator.expression.import.tsx
  type-parameters:
    - match: (<)
      captures:
        1: punctuation.definition.typeparameters.begin.tsx
      push:
        - meta_scope: meta.type.parameters.tsx
        - match: (>)
          captures:
            1: punctuation.definition.typeparameters.end.tsx
          pop: true
        - include: comment
        - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(extends)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
          scope: storage.modifier.tsx
        - include: type
        - include: punctuation-comma
  type-paren-or-function-parameters:
    - match: \(
      captures:
        0: meta.brace.round.tsx
      push:
        - meta_scope: meta.type.paren.cover.tsx
        - match: \)
          captures:
            0: meta.brace.round.tsx
          pop: true
        - include: destructuring-parameter
        - match: |-
            (?x)(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(public|private|protected|readonly)\s+)?(?:(\.\.\.)\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))\s*(\??)(?=\s*(:\s*(
              (<) |
              ([(]\s*(
                ([)]) |
                (\.\.\.) |
                ([_$[:alnum:]]+\s*(
                  ([:,?=])|
                  ([)]\s*=>)
                ))
              ))
            )) |
            (:\s*((<\s*$)|([\(]\s*([\{\[]\s*)?$))))
          captures:
            1: storage.modifier.tsx
            2: keyword.operator.rest.tsx
            3: entity.name.function.tsx variable.language.this.tsx
            4: entity.name.function.tsx
            5: keyword.operator.optional.tsx
        - match: '(?x)(?:(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(public|private|protected|readonly)\s+)?(?:(\.\.\.)\s*)?(?<!=|:)(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(this)|([_$[:alpha:]][_$[:alnum:]]*))\s*(\??)(?=:)'
          captures:
            1: storage.modifier.tsx
            2: keyword.operator.rest.tsx
            3: variable.parameter.tsx variable.language.this.tsx
            4: variable.parameter.tsx
            5: keyword.operator.optional.tsx
        - include: type-annotation
        - match: ","
          scope: punctuation.separator.parameter.tsx
        - include: type
  type-predicate-operator:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))is(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.expression.is.tsx
  type-primitive:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(string|number|boolean|symbol|any|void|never)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: support.type.primitive.tsx
  type-tuple:
    - match: '\['
      captures:
        0: meta.brace.square.tsx
      push:
        - meta_scope: meta.type.tuple.tsx
        - match: '\]'
          captures:
            0: meta.brace.square.tsx
          pop: true
        - include: type
        - include: punctuation-comma
  typeof-operator:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))typeof(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: keyword.operator.expression.typeof.tsx
  undefined-literal:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))undefined(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      scope: constant.language.undefined.tsx
  var-expr:
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(var|let)(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: keyword.control.export.tsx
        2: storage.type.tsx
      push:
        - meta_scope: meta.var.expr.tsx
        - match: '((?=;|}|(\s+(of|in)\s+)|^\s*$|;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)|((?<=\S)(?<!^let|[^\._$[:alnum:]]let|^var|[^\._$[:alnum:]]var)(?=\s*$)))'
          pop: true
        - include: destructuring-variable
        - include: var-single-variable
        - include: variable-initializer
        - include: comment
        - match: (,)\s*(?!\S)
          captures:
            1: punctuation.separator.comma.tsx
          push:
            - match: '(?<!,)(((?==|;|}|(\s+(of|in)\s+)|^\s*$))|((?<=\S)(?=\s*$)))'
              pop: true
            - include: comment
            - include: destructuring-variable
            - include: var-single-variable
            - include: punctuation-comma
        - include: punctuation-comma
    - match: '(?<![_$[:alnum:]])(?:(?<=\.\.\.)|(?<!\.))(?:(\bexport)\s+)?\b(const(?!\s+enum\b))(?![_$[:alnum:]])(?:(?=\.\.\.)|(?!\.))'
      captures:
        1: keyword.control.export.tsx
        2: storage.type.tsx
      push:
        - meta_scope: meta.var.expr.tsx
        - match: '((?=;|}|(\s+(of|in)\s+)|^\s*$|;|^\s*abstract\b|^\s*async\b|^\s*class\b|^\s*const\b|^\s*declare\b|^\s*enum\b|^\s*export\b|^\s*function\b|^\s*import\b|^\s*interface\b|^\s*let\b|^\s*module\b|^\s*namespace\b|^\s*return\b|^\s*type\b|^\s*var\b)|((?<=\S)(?<!^const|[^\._$[:alnum:]]const)(?=\s*$)))'
          pop: true
        - include: destructuring-const
        - include: var-single-const
        - include: variable-initializer
        - include: comment
        - match: (,)\s*(?!\S)
          captures:
            1: punctuation.separator.comma.tsx
          push:
            - match: '(?<!,)(((?==|;|}|(\s+(of|in)\s+)|^\s*$))|((?<=\S)(?=\s*$)))'
              pop: true
            - include: comment
            - include: destructuring-const
            - include: var-single-const
            - include: punctuation-comma
        - include: punctuation-comma
  var-single-const:
    - match: |-
        (?x)([_$[:alpha:]][_$[:alnum:]]*)(?=\s*
        # function assignment |
        (=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )) |
        # typeannotation is fn type: < | () | (... | (param: | (param, | (param? | (param= | (param) =>
        (:\s*(
          (<) |
          ([(]\s*(
            ([)]) |
            (\.\.\.) |
            ([_$[:alnum:]]+\s*(
              ([:,?=])|
              ([)]\s*=>)
            ))
          ))
        )) |
        (:\s*((<\s*$)|([\(]\s*([\{\[]\s*)?$))) |
        (:\s*(=>|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(<[^<>]*>)|[^<>(),=])+=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )))
      captures:
        1: meta.definition.variable.tsx variable.other.constant.tsx entity.name.function.tsx
      push:
        - meta_scope: meta.var-single-variable.expr.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: var-single-variable-type-annotation
    - match: "([_$[:alpha:]][_$[:alnum:]]*)"
      captures:
        1: meta.definition.variable.tsx variable.other.constant.tsx
      push:
        - meta_scope: meta.var-single-variable.expr.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: var-single-variable-type-annotation
  var-single-variable:
    - match: |-
        (?x)([_$[:alpha:]][_$[:alnum:]]*)(?=\s*
        # function assignment |
        (=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )) |
        # typeannotation is fn type: < | () | (... | (param: | (param, | (param? | (param= | (param) =>
        (:\s*(
          (<) |
          ([(]\s*(
            ([)]) |
            (\.\.\.) |
            ([_$[:alnum:]]+\s*(
              ([:,?=])|
              ([)]\s*=>)
            ))
          ))
        )) |
        (:\s*((<\s*$)|([\(]\s*([\{\[]\s*)?$))) |
        (:\s*(=>|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(<[^<>]*>)|[^<>(),=])+=\s*(
          ((async\s+)?(
            (function\s*[(<*]) |
            (function\s+) |
            ([_$[:alpha:]][_$[:alnum:]]*\s*=>)
          )) |
          ((async\s*)?(
            ((<\s*$)|([\(]\s*([\{\[]\s*)?$)) |
            # sure shot arrow functions even if => is on new line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?
          [(]\s*
          (
            ([)]\s*:) |                                                                                       # ():
            ((\.\.\.\s*)?[_$[:alpha:]][_$[:alnum:]]*\s*:)                                                                  # [(]param: | [(]...param:
          )
        ) |
        (
          [<]\s*[_$[:alpha:]][_$[:alnum:]]*\s+extends\s*[^=>]                                                              # < typeparam extends
        ) |
        # arrow function possible to detect only with => on same line
        (
          (<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<]|\<\s*([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\(([^\(\)]|(\([^\(\)]*\)))*\))|(\[([^\[\]]|(\[[^\[\]]*\]))*\]))([^=<>]|=[^<])*\>)*>\s*)?                                                                                 # typeparameters
          \(\s*(([_$[:alpha:]]|(\{([^\{\}]|(\{[^\{\}]*\}))*\})|(\[([^\[\]]|(\[[^\[\]]*\]))*\])|(\.\.\.\s*[_$[:alpha:]]))([^()]|(\(([^\(\)]|(\([^\(\)]*\)))*\)))*)?\)   # parameters
          (\s*:\s*([^<>\(\)]|\<[^<>]+\>|\([^\(\)]+\))+)?                                                                        # return type
          \s*=>                                                                                               # arrow operator
        )
          ))
        )))
      captures:
        1: meta.definition.variable.tsx entity.name.function.tsx
      push:
        - meta_scope: meta.var-single-variable.expr.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: var-single-variable-type-annotation
    - match: "([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])"
      captures:
        1: meta.definition.variable.tsx variable.other.constant.tsx
      push:
        - meta_scope: meta.var-single-variable.expr.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: var-single-variable-type-annotation
    - match: "([_$[:alpha:]][_$[:alnum:]]*)"
      captures:
        1: meta.definition.variable.tsx variable.other.readwrite.tsx
      push:
        - meta_scope: meta.var-single-variable.expr.tsx
        - match: '(?=$|^|[;,=}]|(\s+(of|in)\s+))'
          pop: true
        - include: var-single-variable-type-annotation
  var-single-variable-type-annotation:
    - include: type-annotation
    - include: string
    - include: comment
  variable-initializer:
    - match: (?<!=|!)(=)(?!=)(?=\s*\S)
      captures:
        1: keyword.operator.assignment.tsx
      push:
        - match: '(?=$|^|[,);}\]])'
          pop: true
        - include: expression
    - match: (?<!=|!)(=)(?!=)
      captures:
        1: keyword.operator.assignment.tsx
      push:
        - match: '(?=[,);}\]])|(?=^\s*$)|(?<=\S)(?<!=)(?=\s*$)'
          pop: true
        - include: expression
